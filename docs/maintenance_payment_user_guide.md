# Maintenance Payment User Guide

## 📱 Getting Started

Welcome to the enhanced Maintenance Payment feature! This guide will help you navigate through the improved payment process and access your payment history.

## 🏠 Accessing Maintenance Payments

### From the Home Screen
1. Open the OneApp application
2. Navigate to the **Household** section
3. Tap on **Maintenance Payment**
4. You'll be taken to the new enhanced payment interface

### From the Main Menu
1. Open the side menu (hamburger icon)
2. Select **Payments** → **Maintenance**
3. Access the payment dashboard

## 💳 Making a Payment

### Step 1: Select Your Bill
- **View Available Bills**: See all your pending maintenance bills in an easy-to-read card format
- **Bill Information**: Each card shows:
  - Bill description (e.g., "Monthly Maintenance")
  - Amount due
  - Due date
  - Payment status
  - Society name and unit number (if available)
- **Select Bill**: Tap on the bill you want to pay
- **Selection Indicator**: Selected bills show a green checkmark with "Selected for payment"

### Step 2: Enter Account Details
- **Email Address**: Enter your registered email address
- **Society Information**: Confirm your society name
- **Account Details**: Enter your bank account information if required
- **Validation**: The form will validate your inputs and show helpful error messages

### Step 3: Review Payment Amount
- **Amount Breakdown**: See a detailed breakdown of your payment:
  - Base maintenance amount
  - Convenience fees (if applicable)
  - GST charges
  - Any discounts applied
  - **Total amount** prominently displayed
- **Calculation Updates**: Amounts update automatically as you make changes

### Step 4: Confirm Payment
- **Review Summary**: Double-check all payment details
- **Terms & Conditions**: Review and accept terms
- **Payment Method**: Select your preferred payment method
- **Final Confirmation**: Tap "Proceed to Pay" to complete

### Step 5: Payment Processing
- **Progress Indicator**: See real-time payment processing status
- **Success/Failure**: Get immediate feedback on payment result
- **Receipt**: Access your payment receipt upon successful completion

## 📊 Payment History

### Accessing Payment History
1. From the Maintenance Payment screen, tap the **History** icon (clock icon) in the top-right corner
2. Or navigate to **Payments** → **History** from the main menu

### Viewing Your Payments
- **Complete History**: See all your past maintenance payments
- **Payment Details**: Each entry shows:
  - Payment amount
  - Payment status (Success, Failed, Pending)
  - Transaction date and time
  - Transaction ID
  - Payment method used
  - Society name

### Search and Filter Options

#### Search Functionality
- **Search Bar**: Use the search bar at the top to find specific payments
- **Search by**:
  - Transaction ID
  - Society name
  - Payment amount
  - Payment method

#### Filter Options
Tap the **Filter** icon to access advanced filtering:

1. **Payment Status**
   - Success
   - Failed
   - Pending
   - Processing

2. **Date Range**
   - Select custom date ranges
   - Quick options: Last 7 days, Last month, Last 3 months

3. **Payment Method**
   - Online
   - UPI
   - Credit Card
   - Debit Card
   - Net Banking
   - Wallet

#### Managing Filters
- **Active Filters**: See applied filters as chips below the search bar
- **Remove Filters**: Tap the 'X' on any filter chip to remove it
- **Clear All**: Use "Clear All" to remove all filters at once

### Refreshing Data
- **Pull to Refresh**: Pull down on the payment history list to refresh data
- **Auto-refresh**: Data automatically updates when you return to the screen

## 🔍 Payment Details

### Viewing Transaction Details
1. Tap on any payment in your history
2. A detailed view will show:
   - Complete transaction information
   - Payment breakdown
   - Status updates
   - Receipt information

### Transaction Status Meanings
- **✅ Success/Completed**: Payment processed successfully
- **⏳ Pending**: Payment is being processed
- **❌ Failed**: Payment was unsuccessful
- **🔄 Processing**: Payment is currently being processed

## 💡 Tips for Better Experience

### Payment Tips
- **Check Due Dates**: Pay attention to due dates to avoid late fees
- **Verify Details**: Always double-check payment amounts and account details
- **Save Receipts**: Keep digital receipts for your records
- **Network Connection**: Ensure stable internet connection during payment

### Troubleshooting
- **Payment Failed**: Check your account balance and payment method details
- **Bills Not Loading**: Pull to refresh or check your internet connection
- **App Issues**: Restart the app or contact support

### Security Best Practices
- **Secure Connection**: Only make payments on secure networks
- **Verify Amounts**: Always verify payment amounts before confirming
- **Keep Records**: Save transaction IDs and receipts
- **Report Issues**: Immediately report any suspicious transactions

## 📞 Support & Help

### Getting Help
- **In-App Support**: Use the help section within the app
- **Contact Support**: Reach out to customer support for payment issues
- **FAQ Section**: Check the frequently asked questions for quick answers

### Common Issues & Solutions

#### "Bills Not Loading"
- Check internet connection
- Pull to refresh the screen
- Restart the app

#### "Payment Failed"
- Verify account balance
- Check payment method details
- Try a different payment method
- Contact your bank if issues persist

#### "Cannot Find Transaction"
- Use the search function with transaction ID
- Check date filters
- Ensure you're looking in the correct account

### Contact Information
- **Support Email**: <EMAIL>
- **Phone**: 1-800-ONEAPP
- **Help Center**: Available 24/7 within the app

## 🔄 Updates & New Features

### Recent Improvements
- ✅ Enhanced payment flow with step-by-step guidance
- ✅ Improved payment history with advanced search and filtering
- ✅ Better visual design with modern card layouts
- ✅ Real-time payment amount calculations
- ✅ Detailed payment breakdowns

### Coming Soon
- 🔜 Bulk payment options for multiple bills
- 🔜 Scheduled payments and reminders
- 🔜 Payment analytics and insights
- 🔜 Enhanced receipt generation and sharing

---

*For additional help or feedback, please contact our support team. We're here to make your maintenance payment experience as smooth as possible!*
