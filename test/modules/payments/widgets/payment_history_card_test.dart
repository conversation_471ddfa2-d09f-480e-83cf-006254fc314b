import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneapp/modules/payments/widgets/payment_history_card.dart';

void main() {
  group('PaymentHistoryCard', () {
    final samplePayment = {
      'amount': '1500.00',
      'status': 'success',
      'payment_date': '2024-01-15T10:30:00Z',
      'transaction_id': 'TXN123456789',
      'payment_method': 'UPI',
      'society_name': 'Test Society',
    };

    testWidgets('displays payment information correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentHistoryCard(payment: samplePayment),
          ),
        ),
      );

      expect(find.text('₹1500.00'), findsOneWidget);
      expect(find.text('SUCCESS'), findsOneWidget);
      expect(find.text('Test Society'), findsOneWidget);
      expect(find.text('TXN123456789'), findsOneWidget);
      expect(find.text('UPI'), findsOneWidget);
    });

    testWidgets('handles missing payment data gracefully',
        (WidgetTester tester) async {
      final incompletePayment = {
        'amount': '1000.00',
        'status': 'pending',
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentHistoryCard(payment: incompletePayment),
          ),
        ),
      );

      expect(find.text('₹1000.00'), findsOneWidget);
      expect(find.text('PENDING'), findsOneWidget);
      expect(find.text('N/A'), findsOneWidget); // For missing transaction ID
    });

    testWidgets('calls onTap when tapped', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentHistoryCard(
              payment: samplePayment,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(PaymentHistoryCard));
      expect(tapped, isTrue);
    });

    testWidgets('displays correct status colors', (WidgetTester tester) async {
      final successPayment = {...samplePayment, 'status': 'success'};
      final failedPayment = {...samplePayment, 'status': 'failed'};
      final pendingPayment = {...samplePayment, 'status': 'pending'};

      // Test success status
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentHistoryCard(payment: successPayment),
          ),
        ),
      );
      expect(find.text('SUCCESS'), findsOneWidget);

      // Test failed status
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentHistoryCard(payment: failedPayment),
          ),
        ),
      );
      expect(find.text('FAILED'), findsOneWidget);

      // Test pending status
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentHistoryCard(payment: pendingPayment),
          ),
        ),
      );
      expect(find.text('PENDING'), findsOneWidget);
    });
  });

  group('CompactPaymentHistoryCard', () {
    final samplePayment = {
      'amount': '1500.00',
      'status': 'success',
      'payment_date': '2024-01-15T10:30:00Z',
      'society_name': 'Test Society',
    };

    testWidgets('displays compact payment information',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactPaymentHistoryCard(payment: samplePayment),
          ),
        ),
      );

      expect(find.text('₹1500.00'), findsOneWidget);
      expect(find.text('SUCCESS'), findsOneWidget);
      expect(find.text('Test Society'), findsOneWidget);
    });

    testWidgets('calls onTap when tapped', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactPaymentHistoryCard(
              payment: samplePayment,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(CompactPaymentHistoryCard));
      expect(tapped, isTrue);
    });

    testWidgets('displays default text when society name is missing',
        (WidgetTester tester) async {
      final paymentWithoutSociety = {
        'amount': '1500.00',
        'status': 'success',
        'payment_date': '2024-01-15T10:30:00Z',
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactPaymentHistoryCard(payment: paymentWithoutSociety),
          ),
        ),
      );

      expect(find.text('Payment'), findsOneWidget);
    });

    testWidgets('handles invalid amount gracefully',
        (WidgetTester tester) async {
      final paymentWithInvalidAmount = {
        'amount': 'invalid',
        'status': 'success',
        'payment_date': '2024-01-15T10:30:00Z',
        'society_name': 'Test Society',
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactPaymentHistoryCard(payment: paymentWithInvalidAmount),
          ),
        ),
      );

      expect(find.text('₹0.00'), findsOneWidget);
    });

    testWidgets('handles invalid date gracefully', (WidgetTester tester) async {
      final paymentWithInvalidDate = {
        'amount': '1500.00',
        'status': 'success',
        'payment_date': 'invalid-date',
        'society_name': 'Test Society',
      };

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactPaymentHistoryCard(payment: paymentWithInvalidDate),
          ),
        ),
      );

      // Should not crash and should display some date
      expect(find.byType(CompactPaymentHistoryCard), findsOneWidget);
    });
  });
}
