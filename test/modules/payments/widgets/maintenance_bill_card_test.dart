import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneapp/modules/payments/widgets/maintenance_bill_card.dart';

void main() {
  group('MaintenanceBillCard', () {
    final testDate = DateTime(2024, 1, 15);

    testWidgets('displays bill information correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'pending',
              societyName: 'Test Society',
              unitNumber: 'A-101',
            ),
          ),
        ),
      );

      expect(find.text('Monthly Maintenance'), findsOneWidget);
      expect(find.text('₹1500.00'), findsOneWidget);
      expect(find.text('PENDING'), findsOneWidget);
      expect(find.text('Test Society'), findsOneWidget);
      expect(find.text('Unit A-101'), findsOneWidget);
      expect(find.text('Bill ID: BILL123'), findsOneWidget);
    });

    testWidgets('calls onTap when tapped', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'pending',
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(MaintenanceBillCard));
      expect(tapped, isTrue);
    });

    testWidgets('shows selection indicator when selected',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'pending',
              isSelected: true,
            ),
          ),
        ),
      );

      expect(find.text('Selected for payment'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('does not show selection indicator when not selected',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'pending',
              isSelected: false,
            ),
          ),
        ),
      );

      expect(find.text('Selected for payment'), findsNothing);
    });

    testWidgets('displays correct status colors', (WidgetTester tester) async {
      // Test paid status
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'paid',
            ),
          ),
        ),
      );
      expect(find.text('PAID'), findsOneWidget);

      // Test pending status
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'pending',
            ),
          ),
        ),
      );
      expect(find.text('PENDING'), findsOneWidget);

      // Test overdue status
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'overdue',
            ),
          ),
        ),
      );
      expect(find.text('OVERDUE'), findsOneWidget);
    });

    testWidgets('handles optional fields gracefully',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: testDate,
              status: 'pending',
              // societyName and unitNumber are null
            ),
          ),
        ),
      );

      expect(find.text('Monthly Maintenance'), findsOneWidget);
      expect(find.text('₹1500.00'), findsOneWidget);
      expect(find.text('PENDING'), findsOneWidget);
      // Should not crash when optional fields are missing
    });

    testWidgets('formats date correctly', (WidgetTester tester) async {
      final januaryDate = DateTime(2024, 1, 15);
      final decemberDate = DateTime(2024, 12, 25);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: januaryDate,
              status: 'pending',
            ),
          ),
        ),
      );
      expect(find.text('15 Jan 2024'), findsOneWidget);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MaintenanceBillCard(
              billId: 'BILL123',
              description: 'Monthly Maintenance',
              amount: 1500.0,
              dueDate: decemberDate,
              status: 'pending',
            ),
          ),
        ),
      );
      expect(find.text('25 Dec 2024'), findsOneWidget);
    });
  });

  group('CompactMaintenanceBillCard', () {
    testWidgets('displays compact bill information',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactMaintenanceBillCard(
              description: 'Monthly Maintenance',
              amount: 1500.0,
              status: 'pending',
            ),
          ),
        ),
      );

      expect(find.text('Monthly Maintenance'), findsOneWidget);
      expect(find.text('₹1500.00'), findsOneWidget);
      expect(find.text('pending'), findsOneWidget);
    });

    testWidgets('calls onTap when tapped', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactMaintenanceBillCard(
              description: 'Monthly Maintenance',
              amount: 1500.0,
              status: 'pending',
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(CompactMaintenanceBillCard));
      expect(tapped, isTrue);
    });

    testWidgets('handles zero amount correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactMaintenanceBillCard(
              description: 'Free Service',
              amount: 0.0,
              status: 'completed',
            ),
          ),
        ),
      );

      expect(find.text('₹0.00'), findsOneWidget);
    });

    testWidgets('handles large amounts correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactMaintenanceBillCard(
              description: 'Annual Maintenance',
              amount: 50000.50,
              status: 'pending',
            ),
          ),
        ),
      );

      expect(find.text('₹50000.50'), findsOneWidget);
    });
  });
}
