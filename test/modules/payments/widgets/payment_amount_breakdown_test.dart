import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneapp/modules/payments/widgets/payment_amount_breakdown.dart';

void main() {
  group('PaymentAmountBreakdown', () {
    testWidgets('displays base amount correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountBreakdown(
              baseAmount: 1000.0,
              totalAmount: 1000.0,
            ),
          ),
        ),
      );

      expect(find.text('Payment Breakdown'), findsOneWidget);
      expect(find.text('Maintenance Amount'), findsOneWidget);
      expect(find.text('₹1000.00'), findsOneWidget);
      expect(find.text('Total Amount'), findsOneWidget);
    });

    testWidgets('displays convenience fee when provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountBreakdown(
              baseAmount: 1000.0,
              convenienceFee: 50.0,
              totalAmount: 1050.0,
            ),
          ),
        ),
      );

      expect(find.text('Convenience Fee'), findsOneWidget);
      expect(find.text('₹50.00'), findsOneWidget);
      expect(find.text('₹1050.00'), findsOneWidget);
    });

    testWidgets('displays GST when provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountBreakdown(
              baseAmount: 1000.0,
              gst: 180.0,
              totalAmount: 1180.0,
            ),
          ),
        ),
      );

      expect(find.text('GST (18%)'), findsOneWidget);
      expect(find.text('₹180.00'), findsOneWidget);
      expect(find.text('₹1180.00'), findsOneWidget);
    });

    testWidgets('displays discount as negative amount',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountBreakdown(
              baseAmount: 1000.0,
              discount: 100.0,
              totalAmount: 900.0,
            ),
          ),
        ),
      );

      expect(find.text('Discount'), findsOneWidget);
      expect(find.text('-₹100.00'), findsOneWidget);
      expect(find.text('₹900.00'), findsOneWidget);
    });

    testWidgets('displays additional items when provided',
        (WidgetTester tester) async {
      const additionalItems = [
        PaymentBreakdownItem(label: 'Processing Fee', amount: 25.0),
        PaymentBreakdownItem(label: 'Refund', amount: 50.0, isNegative: true),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountBreakdown(
              baseAmount: 1000.0,
              additionalItems: additionalItems,
              totalAmount: 975.0,
            ),
          ),
        ),
      );

      expect(find.text('Processing Fee'), findsOneWidget);
      expect(find.text('₹25.00'), findsOneWidget);
      expect(find.text('Refund'), findsOneWidget);
      expect(find.text('-₹50.00'), findsOneWidget);
      expect(find.text('₹975.00'), findsOneWidget);
    });
  });

  group('CompactPaymentBreakdown', () {
    testWidgets('displays amount and total correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactPaymentBreakdown(
              amount: 1000.0,
              total: 1000.0,
            ),
          ),
        ),
      );

      expect(find.text('Amount'), findsOneWidget);
      expect(find.text('₹1000.00'), findsAtLeastNWidgets(1));
      expect(find.text('Total'), findsOneWidget);
    });

    testWidgets('displays processing fee when provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CompactPaymentBreakdown(
              amount: 1000.0,
              fee: 50.0,
              total: 1050.0,
            ),
          ),
        ),
      );

      expect(find.text('Processing Fee'), findsOneWidget);
      expect(find.text('₹50.00'), findsOneWidget);
      expect(find.text('₹1050.00'), findsOneWidget);
    });
  });

  group('PaymentAmountDisplay', () {
    testWidgets('displays amount with default currency',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountDisplay(
              amount: 1500.50,
            ),
          ),
        ),
      );

      expect(find.text('₹1500.50'), findsOneWidget);
    });

    testWidgets('displays amount with custom currency',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountDisplay(
              amount: 1500.50,
              currency: '\$',
            ),
          ),
        ),
      );

      expect(find.text('\$1500.50'), findsOneWidget);
    });

    testWidgets('displays label when provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PaymentAmountDisplay(
              amount: 1500.50,
              label: 'Total Amount',
            ),
          ),
        ),
      );

      expect(find.text('Total Amount'), findsOneWidget);
      expect(find.text('₹1500.50'), findsOneWidget);
    });
  });

  group('PaymentBreakdownItem', () {
    test('creates item with correct properties', () {
      const item = PaymentBreakdownItem(
        label: 'Test Fee',
        amount: 100.0,
        isNegative: true,
      );

      expect(item.label, equals('Test Fee'));
      expect(item.amount, equals(100.0));
      expect(item.isNegative, isTrue);
    });

    test('defaults isNegative to false', () {
      const item = PaymentBreakdownItem(
        label: 'Test Fee',
        amount: 100.0,
      );

      expect(item.isNegative, isFalse);
    });
  });
}
