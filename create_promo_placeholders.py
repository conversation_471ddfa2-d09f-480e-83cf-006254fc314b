#!/usr/bin/env python3
"""
Create placeholder images for missing promo assets
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_placeholder_image(width, height, text, filename, bg_color=(100, 100, 100), text_color=(255, 255, 255)):
    """Create a placeholder image with text"""
    # Create image
    img = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font, fallback to basic if not available
    try:
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
    except:
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 24)
        except:
            font = ImageFont.load_default()
    
    # Get text size and position
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # Draw text
    draw.text((x, y), text, fill=text_color, font=font)
    
    # Save image
    img.save(filename)
    print(f"Created: {filename}")

def main():
    # Create directory if it doesn't exist
    os.makedirs("assets/images/market/promos", exist_ok=True)
    
    # Create placeholder images
    placeholders = [
        {
            'filename': 'assets/images/market/promos/promo_discount.png',
            'text': 'DISCOUNT\nPROMO',
            'bg_color': (220, 53, 69),  # Bootstrap danger red
        },
        {
            'filename': 'assets/images/market/promos/promo_referral.png', 
            'text': 'REFERRAL\nREWARDS',
            'bg_color': (142, 45, 226),  # Purple gradient start
        },
        {
            'filename': 'assets/images/market/promos/promo_features.png',
            'text': 'NEW\nFEATURES',
            'bg_color': (44, 62, 80),  # Dark blue-gray
        }
    ]
    
    for placeholder in placeholders:
        create_placeholder_image(
            width=400,
            height=200,
            text=placeholder['text'],
            filename=placeholder['filename'],
            bg_color=placeholder['bg_color'],
            text_color=(255, 255, 255)
        )
    
    print("\n✅ All placeholder images created successfully!")
    print("📝 Note: These are placeholder images. Replace with actual promotional images for production.")

if __name__ == "__main__":
    main()
