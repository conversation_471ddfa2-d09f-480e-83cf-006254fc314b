#!/bin/bash

# Create placeholder images for missing promo assets
echo "🎨 Creating placeholder images for promo assets..."

# Create directory if it doesn't exist
mkdir -p assets/images/market/promos

# Function to create a simple colored PNG using ImageMagick (if available)
create_placeholder_with_imagemagick() {
    local filename="$1"
    local color="$2"
    local text="$3"
    
    if command -v convert >/dev/null 2>&1; then
        convert -size 400x200 xc:"$color" -gravity center -pointsize 24 -fill white -annotate +0+0 "$text" "$filename"
        echo "✅ Created: $filename"
        return 0
    else
        return 1
    fi
}

# Function to create a simple colored PNG using Python (fallback)
create_placeholder_with_python() {
    local filename="$1"
    local color="$2"
    local text="$3"
    
    python3 -c "
import struct
import zlib

def create_png(width, height, color_rgb, filename):
    # Create a simple PNG with solid color
    def write_png(buf, width, height):
        width_byte_4 = width * 4
        raw_data = b''.join(
            b'\\x00' + struct.pack('>I', color_rgb[0] << 16 | color_rgb[1] << 8 | color_rgb[2]) * width
            for _ in range(height)
        )
        
        def png_pack(png_tag, data):
            chunk_head = png_tag + data
            return struct.pack('>I', len(data)) + chunk_head + struct.pack('>I', 0xFFFFFFFF & zlib.crc32(chunk_head))
        
        return b''.join([
            b'\\x89PNG\\r\\n\\x1a\\n',
            png_pack(b'IHDR', struct.pack('>IIBBBBB', width, height, 8, 2, 0, 0, 0)),
            png_pack(b'IDAT', zlib.compress(raw_data, 9)),
            png_pack(b'IEND', b'')
        ])
    
    # Simple RGB colors
    colors = {
        'red': (220, 53, 69),
        'purple': (142, 45, 226), 
        'dark': (44, 62, 80)
    }
    
    color_name = '$color'
    if color_name in colors:
        rgb = colors[color_name]
    else:
        rgb = (128, 128, 128)  # Default gray
    
    png_data = create_png(400, 200, rgb, '$filename')
    with open('$filename', 'wb') as f:
        f.write(png_data)

create_png(400, 200, (128, 128, 128), '$filename')
"
    echo "✅ Created: $filename"
}

# Try ImageMagick first, then fallback to Python
create_placeholder() {
    local filename="$1"
    local color="$2"
    local text="$3"
    
    if ! create_placeholder_with_imagemagick "$filename" "$color" "$text"; then
        # Fallback: create a simple solid color PNG
        echo "⚠️ ImageMagick not available, creating simple colored placeholder..."
        
        # Create a simple 1x1 pixel PNG and let Flutter scale it
        printf '\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\x0cIDATx\x9cc\xf8\x0f\x00\x00\x01\x00\x01\x00\x18\xdd\x8d\xb4\x00\x00\x00\x00IEND\xaeB`\x82' > "$filename"
        echo "✅ Created simple placeholder: $filename"
    fi
}

# Create the placeholder images
echo "📁 Creating directory: assets/images/market/promos"

echo "🎨 Creating promo_discount.png..."
create_placeholder "assets/images/market/promos/promo_discount.png" "#dc3545" "DISCOUNT\\nPROMO"

echo "🎨 Creating promo_referral.png..."
create_placeholder "assets/images/market/promos/promo_referral.png" "#8e2de2" "REFERRAL\\nREWARDS"

echo "🎨 Creating promo_features.png..."
create_placeholder "assets/images/market/promos/promo_features.png" "#2c3e50" "NEW\\nFEATURES"

echo ""
echo "✅ All placeholder images created successfully!"
echo "📝 Note: These are placeholder images. Replace with actual promotional images for production."
echo ""
echo "📋 Created files:"
ls -la assets/images/market/promos/
