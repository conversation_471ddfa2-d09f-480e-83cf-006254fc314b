import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:oneapp/core/services/clarity_service.dart';
import 'package:oneapp/core/services/keycloak_service.dart';
import 'package:oneapp/core/services/chsone_auth_service.dart';
import 'package:oneapp/core/services/sso_flutter_maintenance_service.dart';

class MaintenancePaymentPage extends StatefulWidget {
  const MaintenancePaymentPage({super.key});

  @override
  State<MaintenancePaymentPage> createState() => _MaintenancePaymentPageState();
}

class _MaintenancePaymentPageState extends State<MaintenancePaymentPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _societyNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _confirmAccountController = TextEditingController();
  final _ifscController = TextEditingController();

  bool _isCreatingAccount = false;
  bool _agreeToTerms = false;
  bool _isLoadingAccountSuggestions = false;

  List<Map<String, dynamic>> _accountSuggestions = [];
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _initializeService();
    _loadUserEmail();

    // Listen to email changes for account suggestions
    _emailController.addListener(_onEmailChanged);
  }

  Future<void> _initializeService() async {
    try {
      await ClarityService.recordEvent('maintenance_payment_page_viewed');
    } catch (e) {
      print('Clarity tracking error: $e');
    }
  }

  Future<void> _loadUserEmail() async {
    try {
      print('🔑 [UI] Loading user email from authentication...');

      // First try Keycloak authentication
      final userData = await KeycloakService.getUserData();
      if (userData != null && userData['email'] != null) {
        setState(() {
          _emailController.text = userData['email'];
        });
        print('✅ [UI] User email loaded from Keycloak: ${userData['email']}');
        return;
      }

      // Fallback to CHSOne authentication
      final userProfile = await CHSOneAuthService.instance.getUserProfile();
      if (userProfile != null && userProfile['email'] != null) {
        setState(() {
          _emailController.text = userProfile['email'];
        });
        print('✅ [UI] User email loaded from CHSOne: ${userProfile['email']}');
      } else {
        print(
            '⚠️ [UI] No user email found in authentication, will rely on manual input');
      }
    } catch (e) {
      print('❌ [UI] Error loading user email: $e');
    }
  }

  void _onEmailChanged() {
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    _debounce = Timer(const Duration(milliseconds: 800), () {
      final email = _emailController.text.trim();
      if (email.isNotEmpty && email.contains('@')) {
        _getAccountSuggestions(email);
      } else if (email.length >= 2) {
        // Start searching after 2 characters like in your logs
        _getAccountSuggestions(email);
      } else {
        setState(() {
          _accountSuggestions = [];
        });
      }
    });
  }

  Future<void> _getAccountSuggestions(String email) async {
    setState(() {
      _isLoadingAccountSuggestions = true;
    });

    try {
      print('🔍 [UI] Getting account suggestions for: $email');

      // Use SSO-Flutter compatible API to get account suggestions
      final suggestions =
          await SSOFlutterMaintenanceService.getAccountSuggestions(
        email: email,
      );

      setState(() {
        _accountSuggestions = suggestions;
        _isLoadingAccountSuggestions = false;
      });

      print(
          '✅ [UI] Found ${suggestions.length} account suggestions for $email');
    } catch (e) {
      print('❌ Error getting account suggestions: $e');
      setState(() {
        _isLoadingAccountSuggestions = false;
        _accountSuggestions = [];
      });
    }
  }

  void _selectAccount(Map<String, dynamic> account) {
    try {
      // Auto-populate fields from selected account (matching your API response structure)
      setState(() {
        _emailController.text = account['email'] ?? '';
        _societyNameController.text = account['name'] ??
            account['account_details']?['business_name'] ??
            '';

        // Extract bank account details
        final bankAccount = account['bank_account'];
        if (bankAccount != null) {
          _accountNumberController.text =
              bankAccount['account_number']?.toString() ?? '';
          _confirmAccountController.text =
              bankAccount['account_number']?.toString() ?? '';
          _ifscController.text = bankAccount['ifsc_code'] ?? '';
        }

        // Clear suggestions after selection
        _accountSuggestions = [];
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Account selected: ${account['name'] ?? 'Account'}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error selecting account: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _emailController.dispose();
    _societyNameController.dispose();
    _accountNumberController.dispose();
    _confirmAccountController.dispose();
    _ifscController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maintenance Payment'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Maintenance Payment Form
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.add_business, color: Colors.blue),
                          const SizedBox(width: 8),
                          const Text(
                            'Maintenance Payment',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Society Name
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Society Name',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _societyNameController,
                            decoration: InputDecoration(
                              hintText: 'Society name',
                              hintStyle: TextStyle(color: Colors.grey[400]),
                              filled: true,
                              fillColor: Colors.grey[50],
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.blue, width: 2),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 16),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter society name';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Email Id
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Email Id',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: InputDecoration(
                              hintText: 'Email id',
                              hintStyle: TextStyle(color: Colors.grey[400]),
                              filled: true,
                              fillColor: Colors.grey[50],
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.blue, width: 2),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 16),
                              suffixIcon: _isLoadingAccountSuggestions
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: Padding(
                                        padding: EdgeInsets.all(8.0),
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      ),
                                    )
                                  : null,
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your email address';
                              }
                              if (!value.contains('@')) {
                                return 'Please enter a valid email address';
                              }
                              return null;
                            },
                          ),

                          // Account Suggestions (matching your API response structure)
                          if (_accountSuggestions.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            const Text(
                              'Select an existing account:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.green,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...(_accountSuggestions.map((account) => Card(
                                  margin: const EdgeInsets.only(bottom: 8),
                                  color: Colors.green.shade50,
                                  child: ListTile(
                                    leading: const Icon(Icons.account_balance,
                                        color: Colors.blue),
                                    title: Text(
                                      account['name'] ??
                                          account['account_details']
                                              ?['business_name'] ??
                                          'Account',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            'Email: ${account['email'] ?? ''}'),
                                        if (account['bank_account']
                                                ?['account_number'] !=
                                            null)
                                          Text(
                                              'Account: ****${account['bank_account']['account_number'].toString().substring(account['bank_account']['account_number'].toString().length - 4)}'),
                                        if (account['bank_account']
                                                ?['ifsc_code'] !=
                                            null)
                                          Text(
                                              'IFSC: ${account['bank_account']['ifsc_code']}'),
                                      ],
                                    ),
                                    trailing: const Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.green),
                                    onTap: () => _selectAccount(account),
                                  ),
                                ))),
                            const SizedBox(height: 8),
                          ],
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Bank Account Number
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Bank Account Number',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _accountNumberController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              hintText: 'Bank account number',
                              hintStyle: TextStyle(color: Colors.grey[400]),
                              filled: true,
                              fillColor: Colors.grey[50],
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.blue, width: 2),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 16),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter bank account number';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Confirm Bank Account Number
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Confirm Bank Account Number',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _confirmAccountController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              hintText: 'Confirm bank account number',
                              hintStyle: TextStyle(color: Colors.grey[400]),
                              filled: true,
                              fillColor: Colors.grey[50],
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.blue, width: 2),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 16),
                            ),
                            validator: (value) {
                              if (value != _accountNumberController.text) {
                                return 'Account numbers do not match';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // IFSC Code
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'IFSC Code',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _ifscController,
                            decoration: InputDecoration(
                              hintText: 'Ifsc code',
                              hintStyle: TextStyle(color: Colors.grey[400]),
                              filled: true,
                              fillColor: Colors.grey[50],
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(
                                    color: Colors.blue, width: 2),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 16),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter IFSC code';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Terms & Conditions Checkbox
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Checkbox(
                            value: _agreeToTerms,
                            onChanged: (value) {
                              setState(() {
                                _agreeToTerms = value ?? false;
                              });
                            },
                            activeColor: Colors.blue,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _agreeToTerms = !_agreeToTerms;
                                });
                              },
                              child: Padding(
                                padding: const EdgeInsets.only(top: 12),
                                child: RichText(
                                  text: TextSpan(
                                    text: 'I agree to the ',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[700],
                                    ),
                                    children: [
                                      TextSpan(
                                        text: 'Terms & Conditions',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.red[400],
                                          decoration: TextDecoration.none,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 32),

                      // Next Button
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: _agreeToTerms && !_isCreatingAccount
                              ? () => _createSocietyAccount()
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _agreeToTerms
                                ? const Color(0xFF2C3E50)
                                : Colors.grey[300],
                            foregroundColor: Colors.white,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            disabledBackgroundColor: Colors.grey[300],
                            disabledForegroundColor: Colors.grey[500],
                          ),
                          child: _isCreatingAccount
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Text(
                                  'Next',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _createSocietyAccount() async {
    if (!_formKey.currentState!.validate() || !_agreeToTerms) {
      return;
    }

    setState(() {
      _isCreatingAccount = true;
    });

    try {
      print('🏗️ [UI] Creating society account...');

      // Use SSO-Flutter compatible API to create society account
      final result = await SSOFlutterMaintenanceService.createSocietyAccount(
        societyName: _societyNameController.text.trim(),
        email: _emailController.text.trim(),
        accountNumber: _accountNumberController.text.trim(),
        ifscCode: _ifscController.text.trim(),
      );

      print('✅ [UI] Society account created successfully: $result');

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Society account created successfully! Account ID: ${result['account_id'] ?? 'N/A'}'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );

      // Navigate back or to next screen
      Navigator.pop(context);
    } catch (e) {
      print('❌ [UI] Error creating society account: $e');

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Error creating account: ${e.toString().replaceAll('Exception: ', '')}'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    } finally {
      setState(() {
        _isCreatingAccount = false;
      });
    }
  }
}
