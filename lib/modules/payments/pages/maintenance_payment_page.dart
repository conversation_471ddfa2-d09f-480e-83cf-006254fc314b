import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/services/clarity_service.dart';
import '../../../core/services/chsone_auth_service.dart';
import '../../../core/services/sso_flutter_maintenance_service.dart';
import '../../../core/services/keycloak_service.dart';
import '../../../data/models/recharge_models.dart';
import '../../../utils/storage/sso_storage.dart';

class MaintenancePaymentPage extends StatefulWidget {
  const MaintenancePaymentPage({super.key});

  @override
  State<MaintenancePaymentPage> createState() => _MaintenancePaymentPageState();
}

class _MaintenancePaymentPageState extends State<MaintenancePaymentPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _societyNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _confirmAccountController = TextEditingController();
  final _ifscController = TextEditingController();
  final _amountController = TextEditingController();
  final _panController = TextEditingController();
  final _noteController = TextEditingController();

  Timer? _debounce;

  bool _isLoadingAccount = false;
  bool _isCreatingAccount = false;
  bool _showAccountForm = false;
  bool _showPaymentForm = false;
  bool _isLoadingBills = false;
  bool _isLoadingAccountSuggestions = false;
  bool _isLoadingEmails = false;
  bool _isCalculating = false;
  bool _agreeToTerms = false;

  Map<String, dynamic>? _accountDetails;
  Map<String, dynamic>? _amountCalculation;
  List<MaintenancePayment> _maintenanceBills = [];
  List<String> _emailSuggestions = [];
  List<Map<String, dynamic>> _accountSuggestions = [];

  @override
  void initState() {
    super.initState();
    _initializeService();

    // Try to get user email from authentication service
    _loadUserEmail();

    // Load maintenance bills using SSO-Flutter compatible API
    _loadMaintenanceBills();

    // Load email suggestions
    _loadEmailSuggestions();

    // Listen to email changes for autocomplete
    _emailController.addListener(_onEmailChanged);
  }

  void _onEmailChanged() {
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    _debounce = Timer(const Duration(milliseconds: 800), () {
      final email = _emailController.text.trim();
      if (email.isNotEmpty && email.contains('@')) {
        _getAccountSuggestions(email);
      }
    });
  }

  Future<void> _getAccountSuggestions(String email) async {
    setState(() {
      _isLoadingAccountSuggestions = true;
    });

    try {
      // Use SSO-Flutter compatible API to get account suggestions
      final suggestions =
          await SSOFlutterMaintenanceService.getAccountSuggestions(
              email: email);

      setState(() {
        _accountSuggestions = suggestions;
        _isLoadingAccountSuggestions = false;
      });

      print(
          '✅ [UI] Found ${suggestions.length} account suggestions for $email');
    } catch (e) {
      print('❌ Error getting account suggestions: $e');
      setState(() {
        _isLoadingAccountSuggestions = false;
      });
    }
  }

  Future<void> _selectAccount(Map<String, dynamic> account) async {
    setState(() {
      _isLoadingAccount = true;
    });

    try {
      // Auto-populate fields from selected account
      _emailController.text = account['email'] ?? '';
      _societyNameController.text =
          account['business_name'] ?? account['society_name'] ?? '';
      _accountNumberController.text = account['account_number'] ?? '';
      _confirmAccountController.text = account['account_number'] ?? '';
      _ifscController.text = account['ifsc_code'] ?? '';

      setState(() {
        _accountDetails = account;
        _showAccountForm = true;
        _isLoadingAccount = false;
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'Account details loaded for ${account['business_name'] ?? 'Account'}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoadingAccount = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading account: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _initializeService() async {
    try {
      await ClarityService.recordEvent('maintenance_payment_page_viewed');
    } catch (e) {
      print('Clarity tracking error: $e');
    }
  }

  Future<void> _loadUserEmail() async {
    try {
      print('🔑 [UI] Loading user email from authentication...');

      // First try Keycloak authentication
      final userData = await KeycloakService.getUserData();
      if (userData != null && userData['email'] != null) {
        setState(() {
          _emailController.text = userData['email'];
        });
        print('✅ [UI] User email loaded from Keycloak: ${userData['email']}');

        // Automatically get account suggestions for this email
        _getAccountSuggestions(userData['email']);
        return;
      }

      // Fallback to CHSOne auth service
      final authService = CHSOneAuthService.instance;
      final userProfile = await authService.getUserProfile();
      if (userProfile != null && userProfile['email'] != null) {
        setState(() {
          _emailController.text = userProfile['email'];
        });
        print('✅ [UI] User email loaded from CHSOne: ${userProfile['email']}');

        // Automatically get account suggestions for this email
        _getAccountSuggestions(userProfile['email']);
      } else {
        print(
            '⚠️ [UI] No user email found in authentication, will rely on manual input');
      }
    } catch (e) {
      print('❌ [UI] Error loading user email: $e');
    }
  }

  Future<void> _loadEmailSuggestions() async {
    setState(() {
      _isLoadingEmails = true;
    });

    try {
      // Get user profile from SSO storage
      final profile = await SsoStorage.getUserProfile();
      final List<String> suggestions = [];

      // Add primary email if available
      if (profile != null && profile['email'] != null) {
        suggestions.add(profile['email']);
      }

      // Add CHSONE email if different
      final chsoneProfile = await CHSOneAuthService.instance.getUserProfile();
      if (chsoneProfile != null &&
          chsoneProfile['email'] != null &&
          !suggestions.contains(chsoneProfile['email'])) {
        suggestions.add(chsoneProfile['email']);
      }

      // Add any additional emails from profile
      if (profile != null && profile['additional_emails'] != null) {
        for (String email in List<String>.from(profile['additional_emails'])) {
          if (!suggestions.contains(email)) {
            suggestions.add(email);
          }
        }
      }

      setState(() {
        _emailSuggestions = suggestions;
        _isLoadingEmails = false;
      });
    } catch (e) {
      print('Error loading email suggestions: $e');
      setState(() {
        _isLoadingEmails = false;
      });
    }
  }

  /// Load maintenance bills using SSO-Flutter compatible API
  Future<void> _loadMaintenanceBills() async {
    setState(() {
      _isLoadingBills = true;
    });

    try {
      print('📋 [UI] Loading maintenance bills via SSO-Flutter API...');
      final bills = await SSOFlutterMaintenanceService.getMaintenanceInvoices();

      // Convert to maintenance payment format for UI consistency
      final maintenancePayments = bills
          .map((bill) => MaintenancePayment(
                transactionId: bill['invoice_id'] ??
                    bill['id'] ??
                    DateTime.now().millisecondsSinceEpoch.toString(),
                accountId: bill['account_id'] ?? '1',
                accountName:
                    bill['account_name'] ?? bill['society_name'] ?? 'Society',
                amount: (bill['amount'] ?? bill['total_due_amount'] ?? 0.0)
                    .toDouble(),
                totalAmount:
                    (bill['total_amount'] ?? bill['total_due_amount'] ?? 0.0)
                        .toDouble(),
                status: bill['status'] ?? 'pending',
                paymentDate: bill['due_date'] != null
                    ? DateTime.tryParse(bill['due_date']) ?? DateTime.now()
                    : DateTime.now(),
                dueDate: bill['due_date'] != null
                    ? DateTime.tryParse(bill['due_date'])
                    : null,
                description: bill['description'] ?? 'Maintenance Bill',
                orderId: bill['order_id'],
              ))
          .toList();

      setState(() {
        _maintenanceBills = maintenancePayments;
        _isLoadingBills = false;
      });

      print(
          '✅ [UI] Loaded ${maintenancePayments.length} maintenance bills from SSO-Flutter API');
    } catch (e) {
      print('❌ Error loading maintenance bills: $e');
      setState(() {
        _isLoadingBills = false;
      });
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _societyNameController.dispose();
    _accountNumberController.dispose();
    _confirmAccountController.dispose();
    _ifscController.dispose();
    _amountController.dispose();
    _panController.dispose();
    _noteController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Maintenance Payment'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_showAccountForm) ...[
                _buildCreateAccountForm(),
              ],
              if (_showPaymentForm && _accountDetails != null) ...[
                const SizedBox(height: 24),
                _buildPaymentSection(),
              ],
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildCreateAccountForm() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.add_business, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Create Society Account',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Society Name
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Society Name',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _societyNameController,
                  decoration: InputDecoration(
                    hintText: 'Society name',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: Colors.grey[50],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          const BorderSide(color: Colors.blue, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter society name';
                    }
                    return null;
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Email Id
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Email Id',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: 'Email id',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: Colors.grey[50],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          const BorderSide(color: Colors.blue, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    suffixIcon: _isLoadingAccountSuggestions
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: Padding(
                              padding: EdgeInsets.all(8.0),
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          )
                        : null,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your email address';
                    }
                    if (!value.contains('@')) {
                      return 'Please enter a valid email address';
                    }
                    return null;
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Bank Account Number
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bank Account Number',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _accountNumberController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'Bank account number',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: Colors.grey[50],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          const BorderSide(color: Colors.blue, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter bank account number';
                    }
                    return null;
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Confirm Bank Account Number
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Confirm Bank Account Number',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _confirmAccountController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'Confirm bank account number',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: Colors.grey[50],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          const BorderSide(color: Colors.blue, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                  ),
                  validator: (value) {
                    if (value != _accountNumberController.text) {
                      return 'Account numbers do not match';
                    }
                    return null;
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),

            // IFSC Code
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'IFSC Code',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _ifscController,
                  decoration: InputDecoration(
                    hintText: 'Ifsc code',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: Colors.grey[50],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          const BorderSide(color: Colors.blue, width: 2),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter IFSC code';
                    }
                    return null;
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Terms & Conditions Checkbox
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Checkbox(
                  value: _agreeToTerms,
                  onChanged: (value) {
                    setState(() {
                      _agreeToTerms = value ?? false;
                    });
                  },
                  activeColor: Colors.blue,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _agreeToTerms = !_agreeToTerms;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: RichText(
                        text: TextSpan(
                          text: 'I agree to the ',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                          ),
                          children: [
                            TextSpan(
                              text: 'Terms & Conditions',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.red[400],
                                decoration: TextDecoration.none,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Next Button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _agreeToTerms && !_isCreatingAccount
                    ? () => _createSocietyAccount()
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _agreeToTerms
                      ? const Color(0xFF2C3E50)
                      : Colors.grey[300],
                  foregroundColor: Colors.white,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  disabledBackgroundColor: Colors.grey[300],
                  disabledForegroundColor: Colors.grey[500],
                ),
                child: _isCreatingAccount
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Next',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Payment Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Amount field
            TextFormField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: const InputDecoration(
                labelText: 'Amount',
                hintText: 'Enter amount to pay',
                prefixIcon: Icon(Icons.currency_rupee),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter amount';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Please enter valid amount';
                }
                return null;
              },
              onChanged: (value) {
                final amount = double.tryParse(value);
                if (amount != null && amount > 0) {
                  _calculateTotalAmount(amount);
                }
              },
            ),

            if (_amountCalculation != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Amount Breakdown',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Base Amount: ₹${_amountController.text}'),
                    Text(
                        'Processing Fee: ₹${_amountCalculation!['fee']?.toStringAsFixed(2) ?? '0.00'}'),
                    Text(
                      'Total Amount: ₹${_amountCalculation!['amount']?.toStringAsFixed(2) ?? '0.00'}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ],

            // PAN field (required for amounts >= 50,000)
            if (_amountCalculation != null &&
                (_amountCalculation!['amount'] ?? 0) >= 50000) ...[
              const SizedBox(height: 16),
              TextFormField(
                controller: _panController,
                decoration: const InputDecoration(
                  labelText: 'PAN Number (Required for ₹50,000+)',
                  hintText: 'Enter PAN number',
                  prefixIcon: Icon(Icons.credit_card),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (_amountCalculation != null &&
                      (_amountCalculation!['amount'] ?? 0) >= 50000) {
                    if (value == null || value.isEmpty) {
                      return 'PAN is required for amounts ₹50,000 and above';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
            ],

            // Note field
            TextFormField(
              controller: _noteController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Note (Optional)',
                hintText: 'Add any additional notes',
                prefixIcon: Icon(Icons.note),
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 20),

            // Pay button
            SizedBox(
              width: double.infinity,
              child: AppButton(
                label: _amountCalculation != null
                    ? 'Pay ₹${_amountCalculation!['amount']?.toStringAsFixed(2) ?? '0.00'} (SSO-Flutter)'
                    : 'Calculate Amount First',
                onPressed: () => _initiatePayment(),
                disabled: !_canProceedToPayment(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkExistingAccount() async {
    if (_emailController.text.isEmpty) {
      return;
    }

    setState(() {
      _isLoadingAccount = true;
    });

    try {
      // Use SSO-Flutter API to check account
      final accounts =
          await SSOFlutterMaintenanceService.getSocietyAccountDetails(
        email: _emailController.text,
      );

      final accountDetails = accounts.isNotEmpty ? accounts.first : null;

      setState(() {
        _accountDetails = accountDetails;
        _showPaymentForm = true;
        _isLoadingAccount = false;
      });
    } catch (e) {
      print('No account found: $e');
      setState(() {
        _accountDetails = null;
        _isLoadingAccount = false;
      });
    }
  }

  Future<void> _createSocietyAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isCreatingAccount = true;
    });

    try {
      // Use SSO-Flutter API to create account
      final accountDetails =
          await SSOFlutterMaintenanceService.createSocietyAccount(
        societyName: _societyNameController.text,
        email: _emailController.text,
        accountNumber: _accountNumberController.text,
        ifscCode: _ifscController.text,
      );

      setState(() {
        _accountDetails = accountDetails;
        _showAccountForm = false;
        _showPaymentForm = true;
        _isCreatingAccount = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Society account created successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      print('Error creating account: $e');
      setState(() {
        _isCreatingAccount = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error creating account: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _calculateTotalAmount(double amount) async {
    setState(() {
      _isCalculating = true;
    });

    try {
      // Use SSO-Flutter API to calculate amount
      final calculation =
          await SSOFlutterMaintenanceService.calculateTotalSocietyPaymentAmount(
        amount: amount,
      );

      setState(() {
        _amountCalculation = calculation;
        _isCalculating = false;
      });
    } catch (e) {
      print('Error calculating amount: $e');
      setState(() {
        _amountCalculation = {'amount': amount, 'fee': 0.0};
        _isCalculating = false;
      });
    }
  }

  bool _canProceedToPayment() {
    return _accountDetails != null &&
        _amountCalculation != null &&
        _amountController.text.isNotEmpty &&
        (!(_amountCalculation!['amount'] >= 50000) ||
            _panController.text.isNotEmpty);
  }

  Future<void> _initiatePayment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final amount = double.tryParse(_amountController.text);
    if (amount == null ||
        _accountDetails == null ||
        _amountCalculation == null) {
      return;
    }

    await ClarityService.recordEvent('maintenance_payment_initiated', {
      'amount': amount,
      'total_amount': _amountCalculation!['amount'],
      'via_sso_flutter': true,
    });

    try {
      // Use SSO-Flutter API to initiate payment
      final result = await SSOFlutterMaintenanceService.initiateSocietyPayment(
        accountName:
            _accountDetails!['business_name'] ?? _societyNameController.text,
        totalPayableAmount: _amountCalculation!['amount'],
        actualAmount: amount,
        accountId: _accountDetails!['account_id'] ?? '1',
        pan: _panController.text.isNotEmpty ? _panController.text : null,
        note: _noteController.text.isNotEmpty ? _noteController.text : null,
      );

      _showSuccessDialog(result);
    } catch (e) {
      _showErrorDialog(e.toString());
    }
  }

  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 24),
            const SizedBox(width: 8),
            const Text('Payment Initiated'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order ID: ${result['order_id'] ?? 'N/A'}'),
            Text(
                'Amount: ₹${_amountCalculation!['amount']?.toStringAsFixed(2)}'),
            Text('Society: ${_accountDetails!['business_name'] ?? 'N/A'}'),
            const SizedBox(height: 8),
            const Text(
              '✅ Using SSO-Flutter APIs',
              style: TextStyle(
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to payments dashboard
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 24),
            const SizedBox(width: 8),
            const Text('Payment Failed'),
          ],
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
