import 'package:flutter/material.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/style_guide.dart';
import '../../../common_widgets/app_card.dart';

/// A card widget that displays payment history information
class PaymentHistoryCard extends StatelessWidget {
  final Map<String, dynamic> payment;
  final VoidCallback? onTap;

  const PaymentHistoryCard({
    super.key,
    required this.payment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final amount = double.tryParse(payment['amount']?.toString() ?? '0') ?? 0.0;
    final status = payment['status']?.toString() ?? 'unknown';
    final paymentDate =
        DateTime.tryParse(payment['payment_date']?.toString() ?? '') ??
            DateTime.now();
    final transactionId = payment['transaction_id']?.toString() ?? '';
    final paymentMethod = payment['payment_method']?.toString() ?? 'Unknown';
    final societyName = payment['society_name']?.toString() ??
        payment['account_name']?.toString() ??
        '';

    return AppCard(
      onTap: onTap,
      margin: const EdgeInsets.only(bottom: AppStyleGuide.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with amount and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '₹${amount.toStringAsFixed(2)}',
                style: AppStyleGuide.headingMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(status),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppStyleGuide.spacingS),

          // Society/Account name
          if (societyName.isNotEmpty)
            Text(
              societyName,
              style: AppStyleGuide.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

          const SizedBox(height: AppStyleGuide.spacingS),

          // Payment details
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow(
                      Icons.calendar_today,
                      'Date',
                      _formatDate(paymentDate),
                    ),
                    const SizedBox(height: AppStyleGuide.spacingXS),
                    _buildDetailRow(
                      Icons.payment,
                      'Method',
                      paymentMethod,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow(
                      Icons.receipt_long,
                      'Transaction ID',
                      transactionId.isNotEmpty ? transactionId : 'N/A',
                    ),
                    const SizedBox(height: AppStyleGuide.spacingXS),
                    _buildDetailRow(
                      Icons.access_time,
                      'Time',
                      _formatTime(paymentDate),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: AppStyleGuide.spacingXS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppStyleGuide.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: AppStyleGuide.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'paid':
        return AppColors.success;
      case 'failed':
      case 'error':
        return AppColors.error;
      case 'pending':
      case 'processing':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _formatTime(DateTime date) {
    final hour = date.hour.toString().padLeft(2, '0');
    final minute = date.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}

/// A compact version of the payment history card for smaller spaces
class CompactPaymentHistoryCard extends StatelessWidget {
  final Map<String, dynamic> payment;
  final VoidCallback? onTap;

  const CompactPaymentHistoryCard({
    super.key,
    required this.payment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final amount = double.tryParse(payment['amount']?.toString() ?? '0') ?? 0.0;
    final status = payment['status']?.toString() ?? 'unknown';
    final paymentDate =
        DateTime.tryParse(payment['payment_date']?.toString() ?? '') ??
            DateTime.now();
    final societyName = payment['society_name']?.toString() ??
        payment['account_name']?.toString() ??
        '';

    return Container(
      margin: const EdgeInsets.only(bottom: AppStyleGuide.spacingS),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppStyleGuide.borderRadiusM),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: ListTile(
        onTap: onTap,
        contentPadding: AppStyleGuide.paddingAll,
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getStatusColor(status).withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            _getStatusIcon(status),
            color: _getStatusColor(status),
            size: 20,
          ),
        ),
        title: Text(
          societyName.isNotEmpty ? societyName : 'Payment',
          style: AppStyleGuide.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          _formatDate(paymentDate),
          style: AppStyleGuide.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '₹${amount.toStringAsFixed(2)}',
              style: AppStyleGuide.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: 2),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _getStatusColor(status)),
              ),
              child: Text(
                status.toUpperCase(),
                style: TextStyle(
                  color: _getStatusColor(status),
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'paid':
        return AppColors.success;
      case 'failed':
      case 'error':
        return AppColors.error;
      case 'pending':
      case 'processing':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'paid':
        return Icons.check_circle;
      case 'failed':
      case 'error':
        return Icons.error;
      case 'pending':
      case 'processing':
        return Icons.access_time;
      default:
        return Icons.payment;
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
