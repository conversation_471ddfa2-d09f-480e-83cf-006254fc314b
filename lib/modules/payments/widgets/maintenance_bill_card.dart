import 'package:flutter/material.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/style_guide.dart';
import '../../../common_widgets/app_card.dart';
import '../../../common_widgets/app_badge.dart';

/// A card widget that displays maintenance bill information
class MaintenanceBillCard extends StatelessWidget {
  final String billId;
  final String description;
  final double amount;
  final DateTime dueDate;
  final String status;
  final String? societyName;
  final String? unitNumber;
  final VoidCallback? onTap;
  final bool isSelected;

  const MaintenanceBillCard({
    super.key,
    required this.billId,
    required this.description,
    required this.amount,
    required this.dueDate,
    required this.status,
    this.societyName,
    this.unitNumber,
    this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final isOverdue = _isOverdue();
    final statusColor = _getStatusColor();
    
    return AppCard(
      onTap: onTap,
      margin: const EdgeInsets.only(bottom: AppStyleGuide.spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with bill ID and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  description,
                  style: AppStyleGuide.headingSmall.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              AppBadge(
                text: status.toUpperCase(),
                color: statusColor,
                variant: AppBadgeVariant.filled,
              ),
            ],
          ),
          
          const SizedBox(height: AppStyleGuide.spacingS),
          
          // Bill details
          if (societyName != null || unitNumber != null)
            Row(
              children: [
                if (societyName != null) ...[
                  Icon(
                    Icons.business,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: AppStyleGuide.spacingXS),
                  Text(
                    societyName!,
                    style: AppStyleGuide.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
                if (societyName != null && unitNumber != null)
                  const SizedBox(width: AppStyleGuide.spacingM),
                if (unitNumber != null) ...[
                  Icon(
                    Icons.home,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: AppStyleGuide.spacingXS),
                  Text(
                    'Unit $unitNumber',
                    style: AppStyleGuide.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          
          const SizedBox(height: AppStyleGuide.spacingM),
          
          // Amount and due date
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Amount',
                    style: AppStyleGuide.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: AppStyleGuide.spacingXS),
                  Text(
                    '₹${amount.toStringAsFixed(2)}',
                    style: AppStyleGuide.headingMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Due Date',
                    style: AppStyleGuide.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const SizedBox(height: AppStyleGuide.spacingXS),
                  Text(
                    _formatDate(dueDate),
                    style: AppStyleGuide.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isOverdue ? AppColors.error : AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          // Bill ID
          const SizedBox(height: AppStyleGuide.spacingM),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppStyleGuide.spacingS,
              vertical: AppStyleGuide.spacingXS,
            ),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(AppStyleGuide.borderRadiusS),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.receipt_long,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: AppStyleGuide.spacingXS),
                Text(
                  'Bill ID: $billId',
                  style: AppStyleGuide.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
          
          // Selection indicator
          if (isSelected)
            Container(
              margin: const EdgeInsets.only(top: AppStyleGuide.spacingM),
              padding: const EdgeInsets.symmetric(
                horizontal: AppStyleGuide.spacingS,
                vertical: AppStyleGuide.spacingXS,
              ),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppStyleGuide.borderRadiusS),
                border: Border.all(color: AppColors.success),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 16,
                    color: AppColors.success,
                  ),
                  const SizedBox(width: AppStyleGuide.spacingXS),
                  Text(
                    'Selected for payment',
                    style: AppStyleGuide.bodySmall.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  bool _isOverdue() {
    return DateTime.now().isAfter(dueDate) && status.toLowerCase() != 'paid';
  }

  Color _getStatusColor() {
    switch (status.toLowerCase()) {
      case 'paid':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'overdue':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}

/// A compact version of the maintenance bill card for list views
class CompactMaintenanceBillCard extends StatelessWidget {
  final String description;
  final double amount;
  final String status;
  final VoidCallback? onTap;

  const CompactMaintenanceBillCard({
    super.key,
    required this.description,
    required this.amount,
    required this.status,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppStyleGuide.spacingS),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppStyleGuide.borderRadiusM),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: ListTile(
        onTap: onTap,
        contentPadding: AppStyleGuide.paddingAll,
        title: Text(
          description,
          style: AppStyleGuide.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          status,
          style: AppStyleGuide.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        trailing: Text(
          '₹${amount.toStringAsFixed(2)}',
          style: AppStyleGuide.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }
}
