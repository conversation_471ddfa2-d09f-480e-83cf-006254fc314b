import 'package:flutter/material.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/style_guide.dart';
import '../../../common_widgets/app_card.dart';

/// A widget that displays a detailed breakdown of payment amounts
class PaymentAmountBreakdown extends StatelessWidget {
  final double baseAmount;
  final double? convenienceFee;
  final double? gst;
  final double? discount;
  final double totalAmount;
  final String currency;
  final List<PaymentBreakdownItem>? additionalItems;

  const PaymentAmountBreakdown({
    super.key,
    required this.baseAmount,
    this.convenienceFee,
    this.gst,
    this.discount,
    required this.totalAmount,
    this.currency = '₹',
    this.additionalItems,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Breakdown',
            style: AppStyleGuide.headingMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: AppStyleGuide.spacingM),
          
          // Base amount
          _buildBreakdownItem(
            'Maintenance Amount',
            baseAmount,
            isBase: true,
          ),
          
          // Additional items
          if (additionalItems != null)
            ...additionalItems!.map((item) => _buildBreakdownItem(
              item.label,
              item.amount,
              isNegative: item.isNegative,
            )),
          
          // Convenience fee
          if (convenienceFee != null && convenienceFee! > 0)
            _buildBreakdownItem(
              'Convenience Fee',
              convenienceFee!,
            ),
          
          // GST
          if (gst != null && gst! > 0)
            _buildBreakdownItem(
              'GST (18%)',
              gst!,
            ),
          
          // Discount
          if (discount != null && discount! > 0)
            _buildBreakdownItem(
              'Discount',
              discount!,
              isNegative: true,
            ),
          
          const Divider(height: AppStyleGuide.spacingL),
          
          // Total amount
          _buildBreakdownItem(
            'Total Amount',
            totalAmount,
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildBreakdownItem(
    String label,
    double amount, {
    bool isBase = false,
    bool isTotal = false,
    bool isNegative = false,
  }) {
    final textStyle = isTotal
        ? AppStyleGuide.headingMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          )
        : isBase
            ? AppStyleGuide.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              )
            : AppStyleGuide.bodyMedium;

    final amountColor = isTotal
        ? AppColors.primary
        : isNegative
            ? AppColors.success
            : AppColors.textPrimary;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppStyleGuide.spacingXS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: textStyle),
          Text(
            '${isNegative ? '-' : ''}$currency${amount.toStringAsFixed(2)}',
            style: textStyle.copyWith(color: amountColor),
          ),
        ],
      ),
    );
  }
}

/// A data class for additional breakdown items
class PaymentBreakdownItem {
  final String label;
  final double amount;
  final bool isNegative;

  const PaymentBreakdownItem({
    required this.label,
    required this.amount,
    this.isNegative = false,
  });
}

/// A compact version of the payment breakdown for summary views
class CompactPaymentBreakdown extends StatelessWidget {
  final double amount;
  final double? fee;
  final double total;
  final String currency;

  const CompactPaymentBreakdown({
    super.key,
    required this.amount,
    this.fee,
    required this.total,
    this.currency = '₹',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: AppStyleGuide.paddingAll,
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(AppStyleGuide.borderRadiusM),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Amount',
                style: AppStyleGuide.bodyMedium,
              ),
              Text(
                '$currency${amount.toStringAsFixed(2)}',
                style: AppStyleGuide.bodyMedium,
              ),
            ],
          ),
          
          if (fee != null && fee! > 0) ...[
            const SizedBox(height: AppStyleGuide.spacingXS),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Processing Fee',
                  style: AppStyleGuide.bodySmall,
                ),
                Text(
                  '$currency${fee!.toStringAsFixed(2)}',
                  style: AppStyleGuide.bodySmall,
                ),
              ],
            ),
          ],
          
          const Divider(height: AppStyleGuide.spacingM),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total',
                style: AppStyleGuide.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '$currency${total.toStringAsFixed(2)}',
                style: AppStyleGuide.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// A widget that displays payment amount with visual emphasis
class PaymentAmountDisplay extends StatelessWidget {
  final double amount;
  final String currency;
  final String? label;
  final Color? amountColor;

  const PaymentAmountDisplay({
    super.key,
    required this.amount,
    this.currency = '₹',
    this.label,
    this.amountColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: AppStyleGuide.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppStyleGuide.spacingXS),
        ],
        Text(
          '$currency${amount.toStringAsFixed(2)}',
          style: AppStyleGuide.headingLarge.copyWith(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: amountColor ?? AppColors.primary,
          ),
        ),
      ],
    );
  }
}
