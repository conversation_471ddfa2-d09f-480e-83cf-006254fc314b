import 'package:flutter/material.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/style_guide.dart';

/// A widget that displays the current step in a payment process
class PaymentStepIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepLabels;
  final Color activeColor;
  final Color inactiveColor;
  final Color completedColor;

  const PaymentStepIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepLabels,
    this.activeColor = AppColors.primary,
    this.inactiveColor = AppColors.lightGrey,
    this.completedColor = AppColors.success,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: AppStyleGuide.paddingAll,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Step indicators
          Row(
            children: List.generate(totalSteps, (index) {
              return Expanded(
                child: Row(
                  children: [
                    _buildStepCircle(index),
                    if (index < totalSteps - 1)
                      Expanded(child: _buildConnector(index)),
                  ],
                ),
              );
            }),
          ),
          
          const SizedBox(height: AppStyleGuide.spacingS),
          
          // Step labels
          Row(
            children: List.generate(totalSteps, (index) {
              return Expanded(
                child: Text(
                  stepLabels[index],
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: index == currentStep ? FontWeight.w600 : FontWeight.w400,
                    color: _getStepColor(index),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildStepCircle(int index) {
    final isCompleted = index < currentStep;
    final isActive = index == currentStep;
    final color = _getStepColor(index);

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: isCompleted || isActive ? color : Colors.transparent,
        border: Border.all(
          color: color,
          width: 2,
        ),
      ),
      child: Center(
        child: isCompleted
            ? Icon(
                Icons.check,
                size: 16,
                color: Colors.white,
              )
            : Text(
                '${index + 1}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isActive ? Colors.white : color,
                ),
              ),
      ),
    );
  }

  Widget _buildConnector(int index) {
    final isCompleted = index < currentStep;
    return Container(
      height: 2,
      color: isCompleted ? completedColor : inactiveColor,
    );
  }

  Color _getStepColor(int index) {
    if (index < currentStep) {
      return completedColor;
    } else if (index == currentStep) {
      return activeColor;
    } else {
      return inactiveColor;
    }
  }
}

/// A simplified linear progress indicator for payment steps
class PaymentProgressBar extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final Color activeColor;
  final Color backgroundColor;

  const PaymentProgressBar({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    this.activeColor = AppColors.primary,
    this.backgroundColor = AppColors.lightGrey,
  });

  @override
  Widget build(BuildContext context) {
    final progress = (currentStep + 1) / totalSteps;
    
    return Container(
      height: 4,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(2),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress,
        child: Container(
          decoration: BoxDecoration(
            color: activeColor,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
      ),
    );
  }
}
