import 'package:flutter/material.dart';
import '../../../core/theme/colors.dart';
import '../../../core/theme/style_guide.dart';
import '../../../common_widgets/app_button.dart';

/// A bottom sheet widget for filtering payment history
class PaymentFilterSheet extends StatefulWidget {
  final String? selectedStatus;
  final DateTimeRange? selectedDateRange;
  final String? selectedPaymentMethod;
  final Function(String?, DateTimeRange?, String?) onFiltersChanged;

  const PaymentFilterSheet({
    super.key,
    this.selectedStatus,
    this.selectedDateRange,
    this.selectedPaymentMethod,
    required this.onFiltersChanged,
  });

  @override
  State<PaymentFilterSheet> createState() => _PaymentFilterSheetState();
}

class _PaymentFilterSheetState extends State<PaymentFilterSheet> {
  String? _selectedStatus;
  DateTimeRange? _selectedDateRange;
  String? _selectedPaymentMethod;

  final List<String> _statusOptions = [
    'Success',
    'Failed',
    'Pending',
    'Processing',
  ];

  final List<String> _paymentMethodOptions = [
    'Online',
    'UPI',
    'Credit Card',
    'Debit Card',
    'Net Banking',
    'Wallet',
  ];

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.selectedStatus;
    _selectedDateRange = widget.selectedDateRange;
    _selectedPaymentMethod = widget.selectedPaymentMethod;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.lightGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: AppStyleGuide.paddingAll,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Payments',
                  style: AppStyleGuide.headingLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: _clearAllFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(color: AppColors.primary),
                  ),
                ),
              ],
            ),
          ),

          // Filters
          Flexible(
            child: SingleChildScrollView(
              padding: AppStyleGuide.paddingAll,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Filter
                  _buildSectionTitle('Payment Status'),
                  const SizedBox(height: AppStyleGuide.spacingS),
                  _buildStatusFilter(),

                  const SizedBox(height: AppStyleGuide.spacingL),

                  // Date Range Filter
                  _buildSectionTitle('Date Range'),
                  const SizedBox(height: AppStyleGuide.spacingS),
                  _buildDateRangeFilter(),

                  const SizedBox(height: AppStyleGuide.spacingL),

                  // Payment Method Filter
                  _buildSectionTitle('Payment Method'),
                  const SizedBox(height: AppStyleGuide.spacingS),
                  _buildPaymentMethodFilter(),

                  const SizedBox(height: AppStyleGuide.spacingXL),
                ],
              ),
            ),
          ),

          // Action Buttons
          Container(
            padding: AppStyleGuide.paddingAll,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: AppStyleGuide.spacingM),
                Expanded(
                  child: AppButton(
                    label: 'Apply Filters',
                    onPressed: _applyFilters,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppStyleGuide.headingSmall.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Wrap(
      spacing: AppStyleGuide.spacingS,
      runSpacing: AppStyleGuide.spacingS,
      children: _statusOptions.map((status) {
        final isSelected = _selectedStatus == status;
        return FilterChip(
          label: Text(status),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedStatus = selected ? status : null;
            });
          },
          backgroundColor: Colors.white,
          selectedColor: AppColors.primary.withOpacity(0.1),
          checkmarkColor: AppColors.primary,
          side: BorderSide(
            color: isSelected ? AppColors.primary : AppColors.lightGrey,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildDateRangeFilter() {
    return Container(
      width: double.infinity,
      padding: AppStyleGuide.paddingAll,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightGrey),
        borderRadius: BorderRadius.circular(AppStyleGuide.borderRadiusM),
      ),
      child: InkWell(
        onTap: _selectDateRange,
        child: Row(
          children: [
            Icon(
              Icons.date_range,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: AppStyleGuide.spacingS),
            Expanded(
              child: Text(
                _selectedDateRange != null
                    ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                    : 'Select date range',
                style: AppStyleGuide.bodyMedium.copyWith(
                  color: _selectedDateRange != null
                      ? AppColors.textPrimary
                      : AppColors.textSecondary,
                ),
              ),
            ),
            if (_selectedDateRange != null)
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  setState(() {
                    _selectedDateRange = null;
                  });
                },
                color: AppColors.textSecondary,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodFilter() {
    return Wrap(
      spacing: AppStyleGuide.spacingS,
      runSpacing: AppStyleGuide.spacingS,
      children: _paymentMethodOptions.map((method) {
        final isSelected = _selectedPaymentMethod == method;
        return FilterChip(
          label: Text(method),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedPaymentMethod = selected ? method : null;
            });
          },
          backgroundColor: Colors.white,
          selectedColor: AppColors.primary.withOpacity(0.1),
          checkmarkColor: AppColors.primary,
          side: BorderSide(
            color: isSelected ? AppColors.primary : AppColors.lightGrey,
          ),
        );
      }).toList(),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _clearAllFilters() {
    setState(() {
      _selectedStatus = null;
      _selectedDateRange = null;
      _selectedPaymentMethod = null;
    });
  }

  void _applyFilters() {
    widget.onFiltersChanged(
      _selectedStatus,
      _selectedDateRange,
      _selectedPaymentMethod,
    );
    Navigator.pop(context);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
