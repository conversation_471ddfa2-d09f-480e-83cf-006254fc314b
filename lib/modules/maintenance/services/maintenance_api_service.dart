import 'dart:developer';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../core/services/base_api_service.dart';
import '../../../core/models/api_response.dart';
import '../../../core/models/maintenance_models.dart' as core_models;
import '../../../core/services/auth_token_manager.dart';
import '../../../core/services/chsone_operator_headers.dart';
import '../../../core/services/sso_flutter_maintenance_service.dart';
import '../../../utils/storage/sso_storage.dart';
import '../models/maintenance_api_models.dart' as api_models;

/// Enhanced Maintenance API Service with direct CHSONE integration
/// Implements SSO-Flutter compatible endpoints with fallback support
class MaintenanceApiService extends BaseApiService {
  static const String _serviceName = 'MaintenanceApiService';

  // CHSONE API Base URLs (matching working SSO-Flutter endpoints)
  static const String _chsoneOperatorsBaseUrl = 'https://chsone.in/api/v1/';
  static const String _chsoneResidentBaseUrl = 'https://chsone.in/api/v1/';

  // Maintenance API endpoints (new_one_app format)
  static const String _billsEndpoint = '/maintenance/bills';
  static const String _paymentsEndpoint = '/maintenance/payments';
  static const String _calculateEndpoint = '/maintenance/calculate';
  static const String _initiateEndpoint = '/maintenance/initiate';
  static const String _completeEndpoint = '/maintenance/complete';
  static const String _historyEndpoint = '/maintenance/history';
  static const String _accountEndpoint = '/user/account';
  static const String _summaryEndpoint = '/maintenance/summary';

  MaintenanceApiService({
    String? baseUrl,
  }) : super(
          baseUrl: baseUrl ?? 'https://api.yourapp.com', // Default base URL
          serviceName: _serviceName,
        );

  @override
  Future<Map<String, String>> getAuthHeaders() async {
    final headers = await super.getAuthHeaders();

    try {
      // Add CHSONE-specific authentication
      final userData = await AuthTokenManager.getUserData();
      if (userData != null) {
        headers['X-User-ID'] = userData['user_id']?.toString() ?? '';
        headers['X-Society-ID'] = userData['soc_id']?.toString() ?? '';
        headers['X-Flat-Number'] = userData['flat_number']?.toString() ?? '';
      }

      log('Maintenance headers prepared', name: _serviceName);
      return headers;
    } catch (e) {
      log('Error preparing maintenance headers: $e', name: _serviceName);
      return headers;
    }
  }

  /// Get maintenance bills with direct CHSONE integration
  /// Matches SSO-Flutter's getMaintainceDues() functionality
  Future<ApiResponse<List<core_models.MaintenanceBill>>> getMaintenanceBills({
    String? status,
    String? month,
    int? year,
  }) async {
    try {
      log('📡 [$_serviceName] Fetching maintenance bills from CHSONE API...');

      // Try direct CHSONE API call first (SSO-Flutter compatible)
      try {
        final bills = await _getMaintenanceBillsFromChsone();
        if (bills.isNotEmpty) {
          log('✅ [$_serviceName] Got ${bills.length} bills from CHSONE API');
          return ApiResponse.success(bills,
              message: 'Bills fetched from CHSONE API');
        }
      } catch (e) {
        log('⚠️ [$_serviceName] CHSONE API failed: $e');
      }

      // Fallback to SSO-Flutter service
      try {
        log('🔄 [$_serviceName] Using SSO-Flutter service fallback...');
        final billsData =
            await SSOFlutterMaintenanceService.getMaintenanceInvoices();
        final bills = billsData
            .map((billData) => core_models.MaintenanceBill.fromJson(billData))
            .toList();

        if (bills.isNotEmpty) {
          log('✅ [$_serviceName] Got ${bills.length} bills from SSO-Flutter service');
          return ApiResponse.success(bills,
              message: 'Bills fetched from SSO-Flutter service');
        }
      } catch (e) {
        log('❌ [$_serviceName] SSO-Flutter service failed: $e');
      }

      return ApiResponse.error(
          'Failed to fetch maintenance bills from all sources');
    } catch (e) {
      log('❌ [$_serviceName] Error getting maintenance bills: $e');
      return ApiResponse.error('Failed to fetch maintenance bills: $e');
    }
  }

  /// Direct CHSONE API call for maintenance bills
  Future<List<core_models.MaintenanceBill>>
      _getMaintenanceBillsFromChsone() async {
    // Use exact SSO-Flutter API endpoint (matching working getSocietyAccount pattern)
    final url = '${_chsoneOperatorsBaseUrl}getMaintenanceInvoices';
    log('🔗 [$_serviceName] CHSONE URL: $url');

    // Get headers with x-access-token and x-api-token (sso-flutter style)
    final headers = await ChsOneOperatorHeaders.build();

    final response = await http.get(
      Uri.parse(url),
      headers: headers,
    );

    log('📊 [$_serviceName] CHSONE response status: ${response.statusCode}');

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      log('✅ [$_serviceName] CHSONE bills fetched successfully');

      // Convert response to MaintenanceBill objects
      final billsData = responseData['data'] ?? responseData;
      if (billsData is List) {
        return billsData
            .map((billData) => core_models.MaintenanceBill.fromJson(billData))
            .toList();
      }
    }

    throw Exception(
        'Failed to fetch bills from CHSONE: HTTP ${response.statusCode}');
  }

  /// Get CHSONE authentication token (SSO-Flutter compatible)
  Future<String?> _getChsoneAuthToken() async {
    try {
      // Try multiple token sources in priority order
      String? token;

      // 1. Try SSO Storage (primary for maintenance)
      token = await SsoStorage.getAccessToken();
      if (token != null && token.isNotEmpty) {
        log('🔑 [$_serviceName] Using SSO Storage token');
        return token;
      }

      // 2. Try AuthTokenManager
      token = await AuthTokenManager.getBestAvailableToken();
      if (token != null && token.isNotEmpty) {
        log('🔑 [$_serviceName] Using AuthTokenManager token');
        return token;
      }

      log('⚠️ [$_serviceName] No valid authentication token found');
      return null;
    } catch (e) {
      log('❌ [$_serviceName] Error getting auth token: $e');
      return null;
    }
  }

  /// Calculate maintenance payment amount with direct CHSONE integration
  /// Matches SSO-Flutter's calPayableMaintenanceAmt() functionality
  Future<ApiResponse<api_models.MaintenanceCalculation>>
      calculatePaymentAmount({
    required double amount,
    String? userId,
  }) async {
    try {
      log('🧮 [$_serviceName] Calculating payment amount: $amount');

      // Try direct CHSONE API call first
      try {
        final calculation = await _calculatePaymentAmountFromChsone(amount);
        log('✅ [$_serviceName] Calculation from CHSONE API: ${calculation.totalAmount}');
        return ApiResponse.success(calculation,
            message: 'Calculation from CHSONE API');
      } catch (e) {
        log('⚠️ [$_serviceName] CHSONE calculation failed: $e');
      }

      // Fallback: Create basic calculation
      log('🔄 [$_serviceName] Using basic calculation fallback...');
      final calculation = api_models.MaintenanceCalculation(
        originalAmount: amount,
        calculatedAmount: amount,
        processingFee: amount * 0.02, // 2% processing fee
        convenienceFee: 5.0, // Fixed convenience fee
        totalAmount: amount + (amount * 0.02) + 5.0,
        breakdown: {
          'original_amount': amount,
          'processing_fee': amount * 0.02,
          'convenience_fee': 5.0,
        },
      );

      log('✅ [$_serviceName] Basic calculation: ${calculation.totalAmount}');
      return ApiResponse.success(calculation,
          message: 'Basic calculation applied');
    } catch (e) {
      log('❌ [$_serviceName] Error calculating payment amount: $e');
      return ApiResponse.error('Failed to calculate payment amount: $e');
    }
  }

  /// Direct CHSONE API call for payment calculation
  Future<api_models.MaintenanceCalculation> _calculatePaymentAmountFromChsone(
      double amount) async {
    final token = await _getChsoneAuthToken();
    if (token == null) {
      throw Exception('Authentication required - no valid token');
    }

    // Use exact SSO-Flutter API endpoint
    final url =
        '${_chsoneOperatorsBaseUrl}calculateTotalSocietyPaymentAmount?amount=$amount&token=$token';
    log('🔗 [$_serviceName] CHSONE calculation URL: $url');

    final headers = await ChsOneOperatorHeaders.build();

    final response = await http.get(
      Uri.parse(url),
      headers: headers,
    );

    log('📊 [$_serviceName] CHSONE calculation response: ${response.statusCode}');

    if (response.statusCode == 200) {
      final responseData = json.decode(response.body);
      log('✅ [$_serviceName] CHSONE calculation successful');

      return api_models.MaintenanceCalculation.fromJson(responseData);
    }

    throw Exception(
        'Failed to calculate from CHSONE: HTTP ${response.statusCode}');
  }

  /// Initiate maintenance payment
  Future<ApiResponse<api_models.MaintenancePaymentResponse>> initiatePayment(
    api_models.MaintenancePaymentRequest request,
  ) async {
    try {
      // Validate request
      if (!request.validate()) {
        return ApiResponse.error(
            'Invalid request: ${request.getValidationErrors().join(', ')}');
      }

      final response = await post<api_models.MaintenancePaymentResponse>(
        _initiateEndpoint,
        data: request.toJson(),
        fromJson: (data) =>
            api_models.MaintenancePaymentResponse.fromJson(data),
      );

      // Note: Direct API implementation - no fallback needed for now

      return response;
    } catch (e) {
      log('Error initiating payment: $e', name: _serviceName);
      return ApiResponse.error('Failed to initiate payment: $e');
    }
  }

  /// Complete maintenance payment
  Future<ApiResponse<api_models.MaintenancePaymentResponse>> completePayment(
    api_models.CompletePaymentRequest request,
  ) async {
    try {
      // Validate request
      if (!request.validate()) {
        return ApiResponse.error(
            'Invalid request: ${request.getValidationErrors().join(', ')}');
      }

      final response = await post<api_models.MaintenancePaymentResponse>(
        _completeEndpoint,
        data: request.toJson(),
        fromJson: (data) =>
            api_models.MaintenancePaymentResponse.fromJson(data),
      );

      // Fallback to existing SSO-Flutter service
      // Note: Direct API implementation - no fallback needed for now

      return response;
    } catch (e) {
      log('Error completing payment: $e', name: _serviceName);
      return ApiResponse.error('Failed to complete payment: $e');
    }
  }

  /// Get maintenance payment history
  Future<ApiResponse<List<api_models.MaintenancePayment>>> getPaymentHistory({
    int page = 1,
    int limit = 20,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) queryParams['status'] = status;
      if (fromDate != null) {
        queryParams['from_date'] = fromDate.toIso8601String();
      }
      if (toDate != null) {
        queryParams['to_date'] = toDate.toIso8601String();
      }

      final response = await get<List<api_models.MaintenancePayment>>(
        _historyEndpoint,
        queryParameters: queryParams,
        fromJson: (data) {
          if (data is List) {
            return data
                .map((item) => api_models.MaintenancePayment.fromJson(item))
                .toList();
          }
          return <api_models.MaintenancePayment>[];
        },
      );

      return response;
    } catch (e) {
      log('Error getting payment history: $e', name: _serviceName);
      return ApiResponse.error('Failed to get payment history: $e');
    }
  }

  /// Get society account details
  Future<ApiResponse<api_models.SocietyAccount>> getSocietyAccount() async {
    try {
      final response = await get<api_models.SocietyAccount>(
        _accountEndpoint,
        fromJson: (data) => api_models.SocietyAccount.fromJson(data),
      );

      // Note: Direct API implementation - no fallback needed for now

      return response;
    } catch (e) {
      log('Error getting society account: $e', name: _serviceName);
      return ApiResponse.error('Failed to get society account: $e');
    }
  }

  /// Get maintenance summary
  Future<ApiResponse<api_models.MaintenanceSummary>>
      getMaintenanceSummary() async {
    try {
      final response = await get<api_models.MaintenanceSummary>(
        _summaryEndpoint,
        fromJson: (data) => api_models.MaintenanceSummary.fromJson(data),
      );

      return response;
    } catch (e) {
      log('Error getting maintenance summary: $e', name: _serviceName);
      return ApiResponse.error('Failed to get maintenance summary: $e');
    }
  }

  /// Process offline payment record
  Future<ApiResponse<api_models.MaintenancePaymentResponse>>
      recordOfflinePayment(
    api_models.OfflinePaymentRequest request,
  ) async {
    try {
      // Validate request
      if (!request.validate()) {
        return ApiResponse.error(
            'Invalid request: ${request.getValidationErrors().join(', ')}');
      }

      final response = await post<api_models.MaintenancePaymentResponse>(
        '$_paymentsEndpoint/offline',
        data: request.toJson(),
        fromJson: (data) =>
            api_models.MaintenancePaymentResponse.fromJson(data),
      );

      return response;
    } catch (e) {
      log('Error recording offline payment: $e', name: _serviceName);
      return ApiResponse.error('Failed to record offline payment: $e');
    }
  }

  /// Check service health
  Future<ApiResponse<Map<String, dynamic>>> checkHealth() async {
    try {
      final response = await get<Map<String, dynamic>>(
        '/health',
        fromJson: (data) => data as Map<String, dynamic>,
      );

      return response;
    } catch (e) {
      log('Health check failed: $e', name: _serviceName);
      return ApiResponse.error('Service health check failed: $e');
    }
  }
}
