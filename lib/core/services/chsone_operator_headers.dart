import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer';
import 'onepay_config_service.dart';

/// Utility that builds the standard header map required by CHSOne "operators"
/// endpoints (`https://chsone.in/api/v1/...`).
///
/// These endpoints do **not** use a normal `Authorization: Bearer` header.  They
/// expect the pair of keys that the original sso-flutter app adds automatically
/// (`x-access-token` and `x-api-token`) together with a fixed `X-MClient`
/// identifier.
///
/// The tokens are downloaded and stored inside `SharedPreferences` during the
/// OnePay / SSO bootstrap (see `OnePayConfigService.fetchAndStoreOnePayConfig`).
class ChsOneOperatorHeaders {
  /// Build and return the header map with improved error handling and fallback
  static Future<Map<String, String>> build() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? xAccessToken = prefs.getString('x-access-token');
      String? xApiToken = prefs.getString('x-api-token');

      // If tokens are not found, try to initialize OnePay config
      if (xAccessToken == null || xApiToken == null) {
        log('⚠️ [ChsOneOperatorHeaders] Tokens not found, attempting to initialize OnePay config...');

        try {
          // Try to initialize OnePay configuration
          final configService = OnePayConfigService.instance;
          await configService.initialize();

          // Retry getting tokens after initialization
          final updatedPrefs = await SharedPreferences.getInstance();
          xAccessToken = updatedPrefs.getString('x-access-token');
          xApiToken = updatedPrefs.getString('x-api-token');

          if (xAccessToken != null && xApiToken != null) {
            log('✅ [ChsOneOperatorHeaders] Tokens retrieved after initialization');
          }
        } catch (e) {
          log('❌ [ChsOneOperatorHeaders] Failed to initialize OnePay config: $e');
        }
      }

      // If still no tokens, try to get from OnePayConfigService directly
      if (xAccessToken == null || xApiToken == null) {
        log('⚠️ [ChsOneOperatorHeaders] Trying OnePayConfigService fallback...');

        try {
          final configService = OnePayConfigService.instance;
          final config = await configService.getStoredOnePayConfig();

          xAccessToken = xAccessToken ?? config['x-access-token'];
          xApiToken = xApiToken ?? config['x-api-token'];

          if (xAccessToken != null && xApiToken != null) {
            log('✅ [ChsOneOperatorHeaders] Tokens retrieved from OnePayConfigService');
          }
        } catch (e) {
          log('❌ [ChsOneOperatorHeaders] OnePayConfigService fallback failed: $e');
        }
      }

      // Final check - if still no tokens, throw exception
      if (xAccessToken == null || xApiToken == null) {
        final errorMsg =
            'CHSOne operator tokens not found. Missing: ${xAccessToken == null ? 'x-access-token ' : ''}${xApiToken == null ? 'x-api-token' : ''}';
        log('❌ [ChsOneOperatorHeaders] $errorMsg');
        throw Exception(errorMsg);
      }

      log('✅ [ChsOneOperatorHeaders] Headers built successfully');
      return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'x-access-token': xAccessToken,
        'x-api-token': xApiToken,
        // Same client id the legacy app uses – some back-end rules rely on it.
        'X-MClient': '14',
      };
    } catch (e) {
      log('❌ [ChsOneOperatorHeaders] Error building headers: $e');
      rethrow;
    }
  }
}
