import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import '../constants.dart';
import './chsone_auth_service.dart';
import './keycloak_service.dart';
import './onepay_config_service.dart';
import '../../utils/storage/auth_integration_service.dart';
import '../../utils/storage/sso_storage.dart';
import 'chsone_operator_headers.dart';

/// SSO-Flutter compatible maintenance service
/// Implements the exact API calls and flow from sso-flutter maintenance system
class SSOFlutterMaintenanceService {
  static const String _chsoneOperatorsBaseUrl = 'https://chsone.in/api/v1/';
  static const String _chsoneResidentBaseUrl = 'https://chsone.in/api/v1/';

  /// Get maintenance invoices (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/chsone_api.dart getMaintainceDues()
  static Future<List<Map<String, dynamic>>> getMaintenanceInvoices(
      {int retryCount = 0}) async {
    const maxRetries = 3;

    try {
      log('📡 [SSO-Flutter API] Fetching maintenance invoices... (attempt ${retryCount + 1}/$maxRetries)');

      // Use exact sso-flutter API endpoint (matching working getSocietyAccount pattern)
      final url = '${_chsoneOperatorsBaseUrl}getMaintenanceInvoices';
      log('🔗 [SSO-Flutter API] URL: $url');

      // Get headers with x-access-token and x-api-token (sso-flutter style)
      Map<String, String> headers;
      try {
        headers = await ChsOneOperatorHeaders.build();
        log('✅ [SSO-Flutter API] Headers built successfully');
      } catch (e) {
        log('❌ [SSO-Flutter API] Failed to build headers: $e');

        // If this is not the first attempt and we still can't get headers, give up
        if (retryCount > 0) {
          throw Exception(
              'Authentication configuration error: Unable to build request headers. Please check your login status.');
        }

        // Try to initialize configuration and retry
        log('🔄 [SSO-Flutter API] Attempting to initialize configuration...');
        try {
          final configService = OnePayConfigService.instance;
          await configService.initialize();
          return getMaintenanceInvoices(retryCount: retryCount + 1);
        } catch (configError) {
          throw Exception('Configuration initialization failed: $configError');
        }
      }

      final response = await http
          .get(
            Uri.parse(url),
            headers: headers,
          )
          .timeout(const Duration(seconds: 30));

      log('📊 [SSO-Flutter API] Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Maintenance invoices fetched successfully');

        if (responseData['data'] != null) {
          final invoices =
              List<Map<String, dynamic>>.from(responseData['data']);
          log('📋 [SSO-Flutter API] Found ${invoices.length} maintenance invoices');
          return invoices;
        }

        log('⚠️ [SSO-Flutter API] No invoice data in response');
        return [];
      } else if (response.statusCode == 401) {
        // Try to refresh token
        log('🔄 [SSO-Flutter API] Token expired, attempting refresh...');
        final newToken = await _refreshToken();
        if (newToken != null && retryCount < maxRetries) {
          // Retry with new token
          return getMaintenanceInvoices(retryCount: retryCount + 1);
        }
        throw Exception('Authentication failed - please log in again');
      } else if (response.statusCode == 500) {
        log('❌ [SSO-Flutter API] Server error (500): ${response.body}');

        // Parse server error for more details
        String serverErrorDetails = 'Internal server error';
        try {
          final errorData = json.decode(response.body);
          if (errorData['error'] != null) {
            serverErrorDetails = errorData['error'].toString();
          } else if (errorData['message'] != null) {
            serverErrorDetails = errorData['message'].toString();
          }
        } catch (e) {
          // Use default message if parsing fails
        }

        // Retry on server errors (up to maxRetries)
        if (retryCount < maxRetries) {
          log('🔄 [SSO-Flutter API] Retrying after server error... (${retryCount + 1}/$maxRetries)');
          await Future.delayed(
              Duration(seconds: (retryCount + 1) * 2)); // Exponential backoff
          return getMaintenanceInvoices(retryCount: retryCount + 1);
        }

        throw Exception(
            'Server temporarily unavailable: $serverErrorDetails. Please try again later.');
      } else {
        log('❌ [SSO-Flutter API] Error response: ${response.body}');

        // Parse error response for better user messages
        String errorMessage = 'Failed to fetch maintenance invoices';
        try {
          final errorData = json.decode(response.body);
          if (errorData['error'] != null) {
            errorMessage = errorData['error'].toString();
          } else if (errorData['message'] != null) {
            errorMessage = errorData['message'].toString();
          }
        } catch (e) {
          // Use default message if parsing fails
        }

        if (response.statusCode == 404) {
          throw Exception('No maintenance bills found for your account');
        } else if (response.statusCode == 403) {
          throw Exception(
              'Access denied: You may not have permission to view maintenance bills');
        } else {
          throw Exception('$errorMessage (Error ${response.statusCode})');
        }
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error: $e');

      // If it's already our custom exception, re-throw it
      if (e is Exception && e.toString().contains('Exception:')) {
        rethrow;
      }

      // For network errors, provide user-friendly message
      if (e.toString().contains('TimeoutException') ||
          e.toString().contains('SocketException')) {
        throw Exception(
            'Network connection error: Please check your internet connection and try again');
      }

      // Generic error fallback
      throw Exception('Unable to fetch maintenance bills: ${e.toString()}');
    }
  }

  /// Calculate total society payment amount (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart calPayableMaintenanceAmt()
  static Future<Map<String, dynamic>> calculateTotalSocietyPaymentAmount({
    required double amount,
  }) async {
    try {
      log('💰 [SSO-Flutter API] Calculating total society payment amount...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for calculation');
      }

      final userId = userProfile['user_id'].toString();

      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint and parameters
      final url =
          '${_chsoneOperatorsBaseUrl}calculateTotalSocietyPaymentAmount?amount=$amount&user_id=$userId';
      log('🔗 [SSO-Flutter API] Calculation URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      log('📊 [SSO-Flutter API] Calculation response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Amount calculated successfully');
        return responseData['data'] ?? responseData;
      } else {
        throw Exception(
            'Failed to calculate amount: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error calculating amount: $e');
      rethrow;
    }
  }

  /// Initiate society payment (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart initiateSocietyPayment()
  static Future<Map<String, dynamic>> initiateSocietyPayment({
    required String accountName,
    required double totalPayableAmount,
    required double actualAmount,
    required String accountId,
    String? pan,
    String? note,
  }) async {
    try {
      log('🚀 [SSO-Flutter API] Initiating society payment...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for payment initiation');
      }

      // Build payload exactly like sso-flutter
      final paymentData = {
        'user_id': userProfile['user_id'].toString(),
        'totalPayableAmount': totalPayableAmount.toString(),
        'actualAmount': actualAmount.toString(),
        'account_id': accountId,
        'account_name': accountName,
        'pan': pan ?? '',
        'note': note ?? '',
      };

      log('📋 [SSO-Flutter API] Payment payload: $paymentData');

      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint
      final url = '${_chsoneOperatorsBaseUrl}initiateSocietyPayment';
      log('🔗 [SSO-Flutter API] Initiate URL: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(paymentData),
      );

      log('📊 [SSO-Flutter API] Initiate response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Payment initiated successfully');
        return responseData['data'] ?? responseData;
      } else {
        throw Exception(
            'Failed to initiate payment: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error initiating payment: $e');
      rethrow;
    }
  }

  /// Complete society payment (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart completeSocietyPayment()
  static Future<Map<String, dynamic>> completeSocietyPayment({
    required String orderId,
    required String paymentId,
    required double totalPayableAmount,
    required String accountId,
  }) async {
    try {
      log('✅ [SSO-Flutter API] Completing society payment...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for payment completion');
      }

      // Build payload exactly like sso-flutter
      final completionData = {
        'user_id': userProfile['user_id'].toString(),
        'order_id': orderId,
        'payment_id': paymentId,
        'totalPayableAmount': totalPayableAmount.toString(),
        'account_id': accountId,
      };

      log('📋 [SSO-Flutter API] Completion payload: $completionData');

      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint
      final url = '${_chsoneOperatorsBaseUrl}completeSocietyPayment';
      log('🔗 [SSO-Flutter API] Complete URL: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(completionData),
      );

      log('📊 [SSO-Flutter API] Complete response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        log('🎉 [SSO-Flutter API] Payment completed successfully');
        return responseData['data'] ?? responseData;
      } else {
        throw Exception(
            'Failed to complete payment: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error completing payment: $e');
      rethrow;
    }
  }

  /// Get society account details (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart getSocietyAcDetails()
  static Future<List<Map<String, dynamic>>> getSocietyAccountDetails({
    required String email,
  }) async {
    try {
      log('🏢 [SSO-Flutter API] Fetching society account details...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for account details');
      }

      final userId = userProfile['user_id'].toString();

      // Build header map (includes x-access-token & x-api-token)
      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint and parameters
      final url = '${_chsoneOperatorsBaseUrl}getSocietyAccount';
      log('🔗 [SSO-Flutter API] Account URL: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          ...headers,
          'X-User-ID': userId,
        },
        body: json.encode({
          'user_id': userId,
          'email': email,
          'source': 'CUBEONE',
        }),
      );

      log('📊 [SSO-Flutter API] Account response status: ${response.statusCode}');
      log('📊 [SSO-Flutter API] Account response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Account details fetched successfully');

        if (responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data']);
        }
        return [];
      } else if (response.statusCode == 401) {
        // Try to refresh token
        log('🔄 [SSO-Flutter API] Token expired, attempting refresh...');
        final newToken = await _refreshToken();
        if (newToken != null) {
          // Retry with new token
          return getSocietyAccountDetails(email: email);
        }
        throw Exception('Authentication failed - please log in again');
      } else {
        final errorBody = json.decode(response.body);
        final errorMessage =
            errorBody['message'] ?? errorBody['error'] ?? 'Unknown error';
        throw Exception('Failed to fetch account details: $errorMessage');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error fetching account details: $e');
      rethrow;
    }
  }

  /// Create society account (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart createSocietyAc()
  static Future<Map<String, dynamic>> createSocietyAccount({
    required String societyName,
    required String email,
    required String accountNumber,
    required String ifscCode,
  }) async {
    try {
      log('🏗️ [SSO-Flutter API] Creating society account...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for account creation');
      }

      // Build payload exactly like sso-flutter
      final accountData = {
        'user_id': userProfile['user_id'].toString(),
        'society_name': societyName,
        'email': email,
        'account_number': accountNumber,
        'ifsc_code': ifscCode,
      };

      log('📋 [SSO-Flutter API] Account creation payload: $accountData');

      final headers = await ChsOneOperatorHeaders.build();

      // Use exact sso-flutter API endpoint
      final url = '${_chsoneOperatorsBaseUrl}addSocietyAccount';
      log('🔗 [SSO-Flutter API] Create account URL: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(accountData),
      );

      log('📊 [SSO-Flutter API] Create account response status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        log('🎉 [SSO-Flutter API] Society account created successfully');
        return responseData['data'] ?? responseData;
      } else {
        throw Exception(
            'Failed to create society account: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error creating society account: $e');
      rethrow;
    }
  }

  /// Get account suggestions based on email (sso-flutter compatible)
  /// Matches: sso-flutter/lib/ui/module/sso/utility_bill_payment/maintenance_payment/owner_details.dart getAccountDetails()
  static Future<List<Map<String, dynamic>>> getAccountSuggestions({
    required String email,
  }) async {
    try {
      log('🔍 [SSO-Flutter API] Getting account suggestions for email: $email');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for account suggestions');
      }

      final userId = userProfile['user_id'].toString();

      // Use exact sso-flutter API endpoint
      final url =
          '${_chsoneOperatorsBaseUrl}getSocietyAccount?user_id=$userId&email=$email';
      log('🔗 [SSO-Flutter API] Account suggestions URL: $url');

      final headers = await ChsOneOperatorHeaders.build();

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      log('📊 [SSO-Flutter API] Account suggestions response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Account suggestions fetched successfully');

        if (responseData['data'] != null) {
          // Handle both single account and multiple accounts
          if (responseData['data'] is List) {
            return List<Map<String, dynamic>>.from(responseData['data']);
          } else if (responseData['data'] is Map) {
            return [Map<String, dynamic>.from(responseData['data'])];
          }
        }
        return [];
      } else if (response.statusCode == 404) {
        // No accounts found - this is normal, return empty list
        log('ℹ️ [SSO-Flutter API] No accounts found for email: $email');
        return [];
      } else {
        throw Exception(
            'Failed to fetch account suggestions: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error fetching account suggestions: $e');
      // Return empty list instead of throwing to allow graceful handling
      return [];
    }
  }

  /// Get maintenance payment history (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/sso_api.dart getSocietyPayments()
  static Future<List<Map<String, dynamic>>> getMaintenancePaymentHistory({
    String searchText = '',
  }) async {
    try {
      log('📜 [SSO-Flutter API] Fetching maintenance payment history...');

      // Get user profile (sso-flutter compatible)
      final userProfile = await getSSO_UserProfile();
      if (userProfile == null) {
        throw Exception('User profile required for payment history');
      }

      final userId = userProfile['user_id'].toString();

      // Use exact sso-flutter API endpoint and parameters
      final url =
          '${_chsoneOperatorsBaseUrl}getSocietyPayments?user_id=$userId&search=$searchText';
      log('🔗 [SSO-Flutter API] History URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: await ChsOneOperatorHeaders.build(),
      );

      log('📊 [SSO-Flutter API] History response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Payment history fetched successfully');

        if (responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data']);
        }
        return [];
      } else {
        throw Exception(
            'Failed to fetch payment history: HTTP ${response.statusCode}');
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error fetching payment history: $e');
      rethrow;
    }
  }

  // Private helper methods

  /// Get SSO-Flutter compatible authentication token
  static Future<String?> _getSSO_AuthToken() async {
    try {
      // First try getting token from AuthIntegrationService
      final token = await AuthIntegrationService.getBestAccessToken();
      if (token != null && token.isNotEmpty) {
        log('✅ [SSO-Flutter Auth] Got token from AuthIntegrationService');
        return token;
      }

      // Try CHSONE token
      final chsoneToken =
          await ChsoneAuthService.instance.getChsoneAccessToken();
      if (chsoneToken != null && chsoneToken.isNotEmpty) {
        log('✅ [SSO-Flutter Auth] Got token from CHSONE');
        // Save to SSO storage for future use
        await SsoStorage.saveTokenObject({
          'access_token': chsoneToken,
          'token_type': 'Bearer',
          'synced_from': 'chsone',
          'synced_at': DateTime.now().toIso8601String(),
        });
        return chsoneToken;
      }

      // Try Keycloak token
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null && keycloakToken.isNotEmpty) {
        log('✅ [SSO-Flutter Auth] Got token from Keycloak');
        // Save to SSO storage
        await SsoStorage.saveTokenObject({
          'access_token': keycloakToken,
          'token_type': 'Bearer',
          'synced_from': 'keycloak',
          'synced_at': DateTime.now().toIso8601String(),
        });
        return keycloakToken;
      }

      log('⚠️ [SSO-Flutter Auth] No valid authentication token available');
      return null;
    } catch (e) {
      log('❌ [SSO-Flutter Auth] Error getting auth token: $e');
      return null;
    }
  }

  /// Refresh token
  static Future<String?> _refreshToken() async {
    try {
      log('🔄 [SSO-Flutter Auth] Attempting token refresh...');

      // Try CHSONE refresh first
      final chsoneService = ChsoneAuthService.instance;
      final chsoneToken = await chsoneService.getChsoneAccessToken();
      if (chsoneToken != null) {
        log('✅ [SSO-Flutter Auth] Refreshed CHSONE token');
        await AuthIntegrationService.syncChsoneToSsoStorage();
        return chsoneToken;
      }

      // Try Keycloak refresh
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null) {
        log('✅ [SSO-Flutter Auth] Got new Keycloak token');
        await AuthIntegrationService.syncKeycloakToSsoStorage();
        return keycloakToken;
      }

      log('⚠️ [SSO-Flutter Auth] Token refresh failed');
      return null;
    } catch (e) {
      log('❌ [SSO-Flutter Auth] Error refreshing token: $e');
      return null;
    }
  }

  /// Get SSO-Flutter compatible user profile
  /// Simulates SsoStorage.getUserProfile() from sso-flutter
  static Future<Map<String, dynamic>?> getSSO_UserProfile() async {
    try {
      log('👤 [SSO-Flutter Profile] Getting user profile...');

      // First try getting from SSO storage
      final ssoProfile = await SsoStorage.getUserProfile();
      if (ssoProfile != null) {
        log('✅ [SSO-Flutter] Got user profile from SSO storage');
        return ssoProfile;
      }

      // Try ChsoneAuthService
      final chsoneService = ChsoneAuthService.instance;
      final userProfile = await chsoneService.getUserProfile();
      if (userProfile != null) {
        log('✅ [SSO-Flutter] Got user profile from CHSOneAuthService');
        await SsoStorage.saveUserProfile(userProfile);
        return userProfile;
      }

      // Try Keycloak
      final keycloakToken = await KeycloakService.getAccessToken();
      if (keycloakToken != null) {
        final userInfo = await KeycloakService.getUserInfo();
        if (userInfo != null) {
          log('✅ [SSO-Flutter] Got user profile from Keycloak');
          await SsoStorage.saveUserProfile(userInfo);
          return userInfo;
        }
      }

      log('⚠️ [SSO-Flutter] No user profile available');
      return null;
    } catch (e) {
      log('❌ [SSO-Flutter] Error getting user profile: $e');
      return null;
    }
  }

  /// Get society payments history (sso-flutter compatible)
  /// Matches: sso-flutter/lib/utils/network/chsone_api.dart getSocietyPayments()
  static Future<List<Map<String, dynamic>>> getSocietyPayments({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      log('📡 [SSO-Flutter API] Fetching society payments (page: $page, limit: $limit)...');

      // Use exact sso-flutter API endpoint
      final url =
          '${_chsoneResidentBaseUrl}society/payments?page=$page&limit=$limit';
      log('🔗 [SSO-Flutter API] URL: $url');

      // Get headers with x-access-token and x-api-token (sso-flutter style)
      final headers = await ChsOneOperatorHeaders.build();

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      log('📊 [SSO-Flutter API] Response status: ${response.statusCode}');
      log('📊 [SSO-Flutter API] Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('✅ [SSO-Flutter API] Society payments fetched successfully');

        if (responseData['data'] != null) {
          return List<Map<String, dynamic>>.from(responseData['data']);
        }
        return [];
      } else if (response.statusCode == 401) {
        // Try to refresh token
        log('🔄 [SSO-Flutter API] Token expired, attempting refresh...');
        final newToken = await _refreshToken();
        if (newToken != null) {
          // Retry with new token
          return getSocietyPayments(page: page, limit: limit);
        }
        throw Exception('Authentication failed - please log in again');
      } else {
        log('❌ [SSO-Flutter API] Error response: ${response.body}');

        // Parse error response for better user messages
        String errorMessage = 'Failed to fetch payment history';
        try {
          final errorData = json.decode(response.body);
          if (errorData['error'] != null) {
            errorMessage = errorData['error'].toString();
          } else if (errorData['message'] != null) {
            errorMessage = errorData['message'].toString();
          }
        } catch (e) {
          // Use default message if parsing fails
        }

        if (response.statusCode == 500) {
          throw Exception('Server error: Please try again later');
        } else if (response.statusCode == 404) {
          throw Exception('No payment history found');
        } else {
          throw Exception('$errorMessage (${response.statusCode})');
        }
      }
    } catch (e) {
      log('❌ [SSO-Flutter API] Error: $e');
      throw e;
    }
  }
}
