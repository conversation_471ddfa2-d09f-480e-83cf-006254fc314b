class AppConfig {
  // Environment-based configuration
  static const bool isProduction =
      bool.fromEnvironment('PRODUCTION', defaultValue: false);
  static const bool isDebug = bool.fromEnvironment('DEBUG', defaultValue: true);

  // API Configuration - Using working SSO-Flutter compatible URL
  static const String apiBaseUrl = 'https://chsone.in/api/v1';

  // OnePay API Configuration - These should be loaded from secure storage or environment
  // For development, we'll use fallback values, but in production these should come from secure config
  static String get onePayApiToken {
    // In production, this should come from secure storage or environment variables
    const token = String.fromEnvironment('ONEPAY_API_TOKEN');
    if (token.isNotEmpty) {
      print('✅ [AppConfig] Using OnePay API token from environment');
      return token;
    }

    // Development fallback - updated with potentially working token
    if (isDebug) {
      print('⚠️ [AppConfig] Using development fallback OnePay API token');
      // Try multiple known tokens in order of preference
      const fallbackTokens = [
        'd7feWMBQp2cu731FvQ0HUzYrryE2SRhX', // Kong API token (original)
        'cZszxCPONIhXjvLMNPKaeanbxAIkDaYO', // Access token alternative
        'YOUR_API_TOKEN', // Postman collection placeholder
      ];

      // Return the first available token
      for (final fallbackToken in fallbackTokens) {
        if (fallbackToken.isNotEmpty && fallbackToken != 'YOUR_API_TOKEN') {
          print(
              '🔑 [AppConfig] Using fallback token: ${fallbackToken.substring(0, 8)}...');
          return fallbackToken;
        }
      }
    }

    throw Exception(
        'OnePay API token not configured. Please set ONEPAY_API_TOKEN environment variable.');
  }

  static String get onePayAccessToken {
    // In production, this should come from secure storage or environment variables
    const token = String.fromEnvironment('ONEPAY_ACCESS_TOKEN');
    if (token.isNotEmpty) {
      print('✅ [AppConfig] Using OnePay access token from environment');
      return token;
    }

    // Development fallback
    if (isDebug) {
      print('⚠️ [AppConfig] Using development fallback OnePay access token');
      const fallbackTokens = [
        'cZszxCPONIhXjvLMNPKaeanbxAIkDaYO', // Access token (original)
        'd7feWMBQp2cu731FvQ0HUzYrryE2SRhX', // Kong API token alternative
      ];

      for (final fallbackToken in fallbackTokens) {
        if (fallbackToken.isNotEmpty) {
          print(
              '🔑 [AppConfig] Using fallback access token: ${fallbackToken.substring(0, 8)}...');
          return fallbackToken;
        }
      }
    }

    throw Exception(
        'OnePay access token not configured. Please set ONEPAY_ACCESS_TOKEN environment variable.');
  }

  // App Information
  static const String appName = 'Society Management App';
  static const String appVersion = '1.0.0';

  // Navigation Routes
  static const String routeLogin = '/login';
  static const String routeDashboard = '/dashboard';
  static const String routeHousehold = '/household';
  static const String routePayments = '/payments';
  static const String routeMeetings = '/meetings';
  static const String routeWork = '/work';
  static const String routeProfile = '/profile';

  // Dashboard Tab Indices
  static const int tabHousehold = 0;
  static const int tabPayments = 1;
  static const int tabWork = 2;
  static const int tabProfile = 3;

  // Mock Data Keys
  static const String keyUser = 'user_data';
  static const String keySocieties = 'societies_data';
  static const String keyNotices = 'notices_data';
  static const String keyComplaints = 'complaints_data';
  static const String keyVisitors = 'visitors_data';
  static const String keyStaff = 'staff_data';
}
