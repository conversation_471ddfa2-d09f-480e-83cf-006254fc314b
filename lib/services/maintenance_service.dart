import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../core/constants.dart';
import '../utils/storage/auth_integration_service.dart';

/// Custom exception types for better error handling
class MaintenanceServiceException implements Exception {
  final String message;
  final String code;
  final int? statusCode;

  MaintenanceServiceException(this.message, this.code, [this.statusCode]);

  @override
  String toString() => 'MaintenanceServiceException: $message (Code: $code)';
}

class AuthenticationException extends MaintenanceServiceException {
  AuthenticationException(String message, [int? statusCode])
      : super(message, 'AUTH_ERROR', statusCode);
}

class NetworkException extends MaintenanceServiceException {
  NetworkException(String message, [int? statusCode])
      : super(message, 'NETWORK_ERROR', statusCode);
}

class ValidationException extends MaintenanceServiceException {
  ValidationException(String message, [int? statusCode])
      : super(message, 'VALIDATION_ERROR', statusCode);
}

class ServerException extends MaintenanceServiceException {
  ServerException(String message, [int? statusCode])
      : super(message, 'SERVER_ERROR', statusCode);
}

/// Enhanced Maintenance Service with unified authentication
/// Following sso-flutter patterns for better API integration
class MaintenanceService {
  static final MaintenanceService _instance = MaintenanceService._internal();
  static MaintenanceService get instance => _instance;
  MaintenanceService._internal();

  static const String _baseUrl = AppConstants.apiBaseUrl;
  static const String _tag = 'MaintenanceService';

  /// Check authentication status using unified auth service
  static Future<void> _checkAuthentication() async {
    try {
      final isAuthenticated = await AuthIntegrationService.isAuthenticated();
      if (!isAuthenticated) {
        log('❌ [$_tag] User not authenticated in any system');
        throw AuthenticationException('Please log in to continue');
      }
    } catch (e) {
      log('❌ [$_tag] Authentication check failed: $e');
      if (e is AuthenticationException) rethrow;
      throw AuthenticationException('Authentication service unavailable');
    }
  }

  /// Get authentication headers using unified auth service
  static Future<Map<String, String>> _getAuthHeaders() async {
    try {
      final headers = await AuthIntegrationService.getAuthHeaders();
      log('✅ [$_tag] Got authentication headers');
      return headers;
    } catch (e) {
      log('❌ [$_tag] Error getting auth headers: $e');
      throw AuthenticationException(
          'Authentication failed - please log in again');
    }
  }

  /// Handle API response with comprehensive error handling
  static dynamic _handleResponse(http.Response response, String operation) {
    log('📊 [$_tag] $operation response status: ${response.statusCode}');

    if (kDebugMode) {
      log('📊 [$_tag] $operation response body: ${response.body}');
    }

    try {
      switch (response.statusCode) {
        case 200:
        case 201:
          return _parseSuccessResponse(response);

        case 400:
          throw ValidationException(_extractErrorMessage(response) ??
              'Invalid request - please check your input');

        case 401:
          log('🔑 [$_tag] Authentication failed (401) - user may need to log in again');
          throw AuthenticationException(
              'Your session has expired. Please log in again');

        case 403:
          log('🔒 [$_tag] Access forbidden (403) - insufficient permissions');
          throw AuthenticationException(
              'You don\'t have permission to perform this action');

        case 404:
          log('🔍 [$_tag] Resource not found (404)');
          throw NetworkException(
              'The requested information could not be found');

        case 408:
          throw NetworkException(
              'Request timeout - please check your internet connection and try again');

        case 422:
          log('📋 [$_tag] Validation error (422)');
          throw ValidationException(_extractErrorMessage(response) ??
              'Please check your input and try again');

        case 429:
          throw NetworkException(
              'Too many requests - please wait a moment before trying again');

        case 500:
        case 502:
        case 503:
          log('⚠️ [$_tag] Server error (${response.statusCode})');
          throw ServerException(
              'Our servers are experiencing issues. Please try again later');

        case 504:
          throw NetworkException(
              'Server timeout - please check your internet connection and try again');

        default:
          log('❌ [$_tag] API call failed with status: ${response.statusCode}');
          throw NetworkException(
              'Something went wrong. Please try again (Error: ${response.statusCode})');
      }
    } catch (e) {
      if (e is MaintenanceServiceException) rethrow;
      log('❌ [$_tag] Unexpected error processing response: $e');
      throw ServerException(
          'An unexpected error occurred while processing the response');
    }
  }

  static dynamic _parseSuccessResponse(http.Response response) {
    try {
      final responseData = json.decode(response.body);

      // Handle different response formats from sso-flutter
      if (responseData is Map<String, dynamic>) {
        // Standard API response format
        if (responseData['data'] != null) {
          log('✅ [$_tag] Operation successful');
          return responseData['data'];
        }

        // Direct response format (some APIs return data directly)
        if (responseData['success'] == true) {
          log('✅ [$_tag] Operation successful');
          return responseData['result'] ?? responseData;
        }

        // Response is the data itself
        log('✅ [$_tag] Operation successful');
        return responseData;
      }

      // Response is a list
      if (responseData is List) {
        log('✅ [$_tag] Operation successful');
        return responseData;
      }

      // Fallback
      log('✅ [$_tag] Operation successful');
      return responseData;
    } catch (e) {
      log('❌ [$_tag] Error parsing API response: $e');
      throw ServerException('Error processing server response');
    }
  }

  static String? _extractErrorMessage(http.Response response) {
    try {
      final errorData = json.decode(response.body);
      if (errorData is Map<String, dynamic>) {
        return errorData['message'] ??
            errorData['error'] ??
            errorData['detail'] ??
            errorData['msg'];
      }
    } catch (e) {
      log('❌ [$_tag] Could not parse error message: $e');
    }
    return null;
  }

  /// Handle network connectivity and timeout errors
  static Future<T> _executeWithRetry<T>(Future<T> Function() operation,
      {int maxRetries = 2}) async {
    int retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        return await operation();
      } on SocketException {
        if (retryCount == maxRetries) {
          throw NetworkException(
              'No internet connection. Please check your network and try again');
        }
        retryCount++;
        await Future.delayed(Duration(seconds: retryCount * 2));
      } on TimeoutException {
        if (retryCount == maxRetries) {
          throw NetworkException(
              'Connection timeout. Please check your internet connection and try again');
        }
        retryCount++;
        await Future.delayed(Duration(seconds: retryCount * 2));
      } on HttpException catch (e) {
        throw NetworkException('Network error: ${e.message}');
      } catch (e) {
        if (e is MaintenanceServiceException) rethrow;
        if (retryCount == maxRetries) {
          log('❌ [$_tag] Operation failed after $maxRetries retries: $e');
          throw ServerException('Operation failed after multiple attempts');
        }
        retryCount++;
        await Future.delayed(Duration(seconds: retryCount * 2));
      }
    }

    throw ServerException('Operation failed');
  }

  /// Get all maintenance bills with unified authentication
  static Future<List<Map<String, dynamic>>> getMaintenanceBills() async {
    return _executeWithRetry(() async {
      try {
        log('📡 [$_tag] Fetching maintenance invoices from API...');

        // Check authentication first
        await _checkAuthentication();

        // Get authentication headers
        final headers = await _getAuthHeaders();

        // Try multiple endpoint variations based on working SSO-Flutter patterns
        final endpoints = [
          '$_baseUrl/getMaintenanceInvoices',
          '$_baseUrl/getSocietyAccount', // Test with working endpoint pattern
          '$_baseUrl/maintenance/invoices',
          '$_baseUrl/maintenance/bills',
        ];

        for (final endpoint in endpoints) {
          try {
            log('🔗 [$_tag] Trying endpoint: $endpoint');

            final response = await http
                .get(
                  Uri.parse(endpoint),
                  headers: headers,
                )
                .timeout(const Duration(seconds: 30));

            final data = _handleResponse(response, 'Get maintenance bills');

            if (data is List) {
              return data.map((bill) => bill as Map<String, dynamic>).toList();
            } else if (data is Map<String, dynamic> && data['bills'] != null) {
              return (data['bills'] as List)
                  .map((bill) => bill as Map<String, dynamic>)
                  .toList();
            }

            // If we get here, the endpoint worked but format is unexpected
            log('⚠️ [$_tag] Unexpected response format from $endpoint');
            continue;
          } catch (e) {
            log('❌ [$_tag] Error with endpoint $endpoint: $e');
            if (endpoint == endpoints.last) {
              // This was the last endpoint, re-throw the error
              rethrow;
            }
            // Try the next endpoint
            continue;
          }
        }

        throw NetworkException(
            'Unable to fetch maintenance bills. Please try again later');
      } catch (e) {
        log('❌ [$_tag] Error fetching bills: $e');
        if (e is MaintenanceServiceException) rethrow;
        throw ServerException('Failed to load maintenance bills');
      }
    });
  }

  /// Get maintenance bill by ID
  static Future<Map<String, dynamic>> getMaintenanceBillById(
      String billId) async {
    try {
      log('📡 [$_tag] Fetching bill details for ID: $billId');

      // Check authentication first
      await _checkAuthentication();

      final headers = await _getAuthHeaders();

      // Try multiple endpoint patterns
      final endpoints = [
        '$_baseUrl/maintenance/bills/$billId',
        '$_baseUrl/maintenance/invoices/$billId',
        '$_baseUrl/bills/$billId',
        '$_baseUrl/invoices/$billId',
      ];

      for (final endpoint in endpoints) {
        try {
          log('🔗 [$_tag] Trying endpoint: $endpoint');

          final response = await http
              .get(
                Uri.parse(endpoint),
                headers: headers,
              )
              .timeout(const Duration(seconds: 30));

          final data = _handleResponse(response, 'Get bill by ID');

          if (data is Map<String, dynamic>) {
            return data;
          }
        } catch (e) {
          log('❌ [$_tag] Error with endpoint $endpoint: $e');
          if (endpoint == endpoints.last) {
            throw e;
          }
          continue;
        }
      }

      throw Exception('Unable to fetch bill details from any endpoint');
    } catch (e) {
      log('❌ [$_tag] Error fetching bill by ID: $e');
      throw e;
    }
  }

  /// Submit payment with unified authentication
  static Future<Map<String, dynamic>> submitPayment({
    required String billId,
    required double amount,
    required String paidBy,
    required String paymentMethod,
    String? chequeNumber,
    String? bankName,
    String? chequeDate,
    String? paymentDate,
    String? depositAccount,
    String? transactionId,
    String? remarks,
  }) async {
    try {
      log('📡 [$_tag] Submitting payment for bill: $billId');

      // Check authentication first
      await _checkAuthentication();

      final headers = await _getAuthHeaders();
      headers['Content-Type'] = 'application/json';

      final paymentData = {
        'bill_id': billId,
        'amount': amount,
        'paid_by': paidBy,
        'payment_method': paymentMethod,
        'payment_date': paymentDate ?? DateTime.now().toIso8601String(),
        if (chequeNumber != null) 'cheque_number': chequeNumber,
        if (bankName != null) 'bank_name': bankName,
        if (chequeDate != null) 'cheque_date': chequeDate,
        if (depositAccount != null) 'deposit_account': depositAccount,
        if (transactionId != null) 'transaction_id': transactionId,
        if (remarks != null) 'remarks': remarks,
      };

      final response = await http
          .post(
            Uri.parse('$_baseUrl/maintenance/payments'),
            headers: headers,
            body: json.encode(paymentData),
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, 'Submit payment');
    } catch (e) {
      log('❌ [$_tag] Error submitting payment: $e');
      throw e;
    }
  }

  /// Upload cheque image with enhanced error handling
  static Future<String> uploadChequeImage(File chequeImage) async {
    try {
      log('📡 [$_tag] Uploading cheque image');

      // Check authentication first
      await _checkAuthentication();

      // Validate file
      if (!chequeImage.existsSync()) {
        throw Exception('Cheque image file does not exist');
      }

      final fileSizeInBytes = chequeImage.lengthSync();
      const maxSizeInBytes = 5 * 1024 * 1024; // 5MB

      if (fileSizeInBytes > maxSizeInBytes) {
        throw Exception('Cheque image file is too large (max 5MB)');
      }

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/maintenance/upload'),
      );

      // Add the authorization header
      final headers = await _getAuthHeaders();
      headers.forEach((key, value) {
        request.headers[key] = value;
      });

      // Add the file
      request.files.add(
        await http.MultipartFile.fromPath('cheque_image', chequeImage.path),
      );

      // Add metadata
      request.fields['file_type'] = 'cheque_image';
      request.fields['upload_date'] = DateTime.now().toIso8601String();

      final streamedResponse =
          await request.send().timeout(const Duration(seconds: 60));
      final response = await http.Response.fromStream(streamedResponse);

      final data = _handleResponse(response, 'Upload cheque image');

      if (data is Map<String, dynamic>) {
        return data['image_url'] ?? data['url'] ?? data['file_url'] ?? '';
      }

      return data.toString();
    } catch (e) {
      log('❌ [$_tag] Error uploading cheque image: $e');
      throw e;
    }
  }

  /// Record an offline payment with enhanced data structure
  static Future<Map<String, dynamic>> recordOfflinePayment(
    Map<String, dynamic> paymentDetails,
  ) async {
    try {
      log('📡 [$_tag] Recording offline payment');

      // Check authentication first
      await _checkAuthentication();

      final headers = await _getAuthHeaders();
      headers['Content-Type'] = 'application/json';

      // Add metadata
      paymentDetails['recorded_at'] = DateTime.now().toIso8601String();
      paymentDetails['payment_type'] = 'offline';

      final response = await http
          .post(
            Uri.parse('$_baseUrl/maintenance/payments/offline'),
            headers: headers,
            body: json.encode(paymentDetails),
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, 'Record offline payment');
    } catch (e) {
      log('❌ [$_tag] Error recording offline payment: $e');
      throw e;
    }
  }

  /// Process online payment with enhanced validation
  static Future<Map<String, dynamic>> processPayment(
    Map<String, dynamic> paymentData,
  ) async {
    try {
      log('📡 [$_tag] Processing online payment');

      // Check authentication first
      await _checkAuthentication();

      final headers = await _getAuthHeaders();
      headers['Content-Type'] = 'application/json';

      // Add metadata
      paymentData['processed_at'] = DateTime.now().toIso8601String();
      paymentData['payment_type'] = 'online';

      final response = await http
          .post(
            Uri.parse('$_baseUrl/maintenance/payments'),
            headers: headers,
            body: json.encode(paymentData),
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, 'Process payment');
    } catch (e) {
      log('❌ [$_tag] Error processing payment: $e');
      throw e;
    }
  }

  /// Get payment history
  static Future<List<Map<String, dynamic>>> getPaymentHistory() async {
    try {
      log('📡 [$_tag] Fetching payment history');

      // Check authentication first
      await _checkAuthentication();

      final headers = await _getAuthHeaders();

      final response = await http
          .get(
            Uri.parse('$_baseUrl/maintenance/payments/history'),
            headers: headers,
          )
          .timeout(const Duration(seconds: 30));

      final data = _handleResponse(response, 'Get payment history');

      if (data is List) {
        return data.map((payment) => payment as Map<String, dynamic>).toList();
      } else if (data is Map<String, dynamic> && data['payments'] != null) {
        return (data['payments'] as List)
            .map((payment) => payment as Map<String, dynamic>)
            .toList();
      }

      return [];
    } catch (e) {
      log('❌ [$_tag] Error fetching payment history: $e');
      throw e;
    }
  }

  /// Get payment receipt
  static Future<Map<String, dynamic>> getPaymentReceipt(
      String paymentId) async {
    try {
      log('📡 [$_tag] Fetching payment receipt for ID: $paymentId');

      // Check authentication first
      await _checkAuthentication();

      final headers = await _getAuthHeaders();

      final response = await http
          .get(
            Uri.parse('$_baseUrl/maintenance/payments/$paymentId/receipt'),
            headers: headers,
          )
          .timeout(const Duration(seconds: 30));

      return _handleResponse(response, 'Get payment receipt');
    } catch (e) {
      log('❌ [$_tag] Error fetching payment receipt: $e');
      throw e;
    }
  }

  /// Initialize authentication on service start
  static Future<void> initialize() async {
    try {
      log('🚀 [$_tag] Initializing maintenance service...');

      // Initialize authentication
      await AuthIntegrationService.initializeAuth();

      log('✅ [$_tag] Maintenance service initialized successfully');
    } catch (e) {
      log('❌ [$_tag] Error initializing maintenance service: $e');
      throw e;
    }
  }
}
